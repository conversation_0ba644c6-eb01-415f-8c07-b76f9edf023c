//+------------------------------------------------------------------+
//|                                                    Dashboard.mqh |
//|                                  Copyright 2024, Fibonacci EA    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Fibonacci EA"
#property link      "https://www.mql5.com"
#property version   "1.00"

#include "RiskControl.mqh"
#include "TradeManager.mqh"
#include "FibonacciEngine.mqh"

//+------------------------------------------------------------------+
//| Dashboard Class                                                 |
//+------------------------------------------------------------------+
class CDashboard
{
private:
   string            m_symbol;
   
   // Panel properties
   string            m_panel_name;
   int               m_panel_x;
   int               m_panel_y;
   int               m_panel_width;
   int               m_panel_height;
   color             m_panel_color;
   color             m_text_color;
   int               m_font_size;
   string            m_font_name;
   
   // References to other modules
   CRiskControl*     m_risk_control;
   CTradeManager*    m_trade_manager;
   CFibonacciEngine* m_fibonacci;
   
   // Button names
   string            m_btn_close_all;
   string            m_btn_enable_trading;
   string            m_btn_disable_trading;
   
   // Display state
   bool              m_visible;
   bool              m_initialized;
   datetime          m_last_update;

public:
   // Constructor
   CDashboard(void);
   ~CDashboard(void);
   
   // Initialization
   bool              Init(string symbol, int x=20, int y=20);
   void              Deinit(void);
   
   // Set references
   void              SetRiskControl(CRiskControl* risk_control) { m_risk_control = risk_control; }
   void              SetTradeManager(CTradeManager* trade_manager) { m_trade_manager = trade_manager; }
   void              SetFibonacci(CFibonacciEngine* fibonacci) { m_fibonacci = fibonacci; }
   
   // Main methods
   bool              Update(void);
   void              Show(void);
   void              Hide(void);
   bool              IsVisible(void) { return m_visible; }
   
   // Event handling
   bool              OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam);

private:
   // Panel creation
   bool              CreatePanel(void);
   bool              CreateLabels(void);
   bool              CreateButtons(void);
   void              RemovePanel(void);
   
   // Display updates
   void              UpdateLabels(void);
   void              UpdateTradeInfo(void);
   void              UpdateRiskInfo(void);
   void              UpdateFibonacciInfo(void);
   void              UpdateSystemInfo(void);
   
   // Button handlers
   void              OnCloseAllTrades(void);
   void              OnEnableTrading(void);
   void              OnDisableTrading(void);
   
   // Utility functions
   bool              CreateLabel(string name, int x, int y, string text, color clr=clrWhite);
   bool              CreateButton(string name, int x, int y, int width, int height, string text, color bg_color=clrBlue);
   void              SetLabelText(string name, string text);
   string            FormatDouble(double value, int digits=2);
   string            FormatPercent(double value);
   color             GetStatusColor(bool status);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CDashboard::CDashboard(void)
{
   m_symbol = "";
   m_panel_name = "EA_Dashboard_" + IntegerToString(GetTickCount());
   m_panel_x = 20;
   m_panel_y = 20;
   m_panel_width = 300;
   m_panel_height = 400;
   m_panel_color = clrDarkSlateGray;
   m_text_color = clrWhite;
   m_font_size = 8;
   m_font_name = "Arial";
   
   m_risk_control = NULL;
   m_trade_manager = NULL;
   m_fibonacci = NULL;
   
   m_btn_close_all = m_panel_name + "_CloseAll";
   m_btn_enable_trading = m_panel_name + "_EnableTrading";
   m_btn_disable_trading = m_panel_name + "_DisableTrading";
   
   m_visible = false;
   m_initialized = false;
   m_last_update = 0;
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CDashboard::~CDashboard(void)
{
   Deinit();
}

//+------------------------------------------------------------------+
//| Initialize dashboard                                            |
//+------------------------------------------------------------------+
bool CDashboard::Init(string symbol, int x=20, int y=20)
{
   m_symbol = symbol;
   m_panel_x = x;
   m_panel_y = y;
   
   if(!CreatePanel())
   {
      Print("Failed to create dashboard panel");
      return false;
   }
   
   if(!CreateLabels())
   {
      Print("Failed to create dashboard labels");
      return false;
   }
   
   if(!CreateButtons())
   {
      Print("Failed to create dashboard buttons");
      return false;
   }
   
   m_initialized = true;
   m_visible = true;
   
   Print("Dashboard initialized for ", m_symbol);
   return true;
}

//+------------------------------------------------------------------+
//| Cleanup dashboard                                              |
//+------------------------------------------------------------------+
void CDashboard::Deinit(void)
{
   RemovePanel();
   m_initialized = false;
   m_visible = false;
}

//+------------------------------------------------------------------+
//| Update dashboard display                                        |
//+------------------------------------------------------------------+
bool CDashboard::Update(void)
{
   if(!m_initialized || !m_visible)
      return false;
      
   // Update only every second to avoid excessive redraws
   datetime current_time = TimeCurrent();
   if(current_time == m_last_update)
      return true;
      
   m_last_update = current_time;
   
   UpdateLabels();
   ChartRedraw();
   
   return true;
}

//+------------------------------------------------------------------+
//| Show dashboard                                                  |
//+------------------------------------------------------------------+
void CDashboard::Show(void)
{
   if(!m_initialized)
      return;
      
   ObjectSetInteger(0, m_panel_name, OBJPROP_TIMEFRAMES, OBJ_ALL_PERIODS);
   m_visible = true;
   Update();
}

//+------------------------------------------------------------------+
//| Hide dashboard                                                  |
//+------------------------------------------------------------------+
void CDashboard::Hide(void)
{
   if(!m_initialized)
      return;
      
   ObjectSetInteger(0, m_panel_name, OBJPROP_TIMEFRAMES, OBJ_NO_PERIODS);
   m_visible = false;
}

//+------------------------------------------------------------------+
//| Handle chart events                                            |
//+------------------------------------------------------------------+
bool CDashboard::OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   if(id == CHARTEVENT_OBJECT_CLICK)
   {
      if(sparam == m_btn_close_all)
      {
         OnCloseAllTrades();
         return true;
      }
      else if(sparam == m_btn_enable_trading)
      {
         OnEnableTrading();
         return true;
      }
      else if(sparam == m_btn_disable_trading)
      {
         OnDisableTrading();
         return true;
      }
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Create main panel                                              |
//+------------------------------------------------------------------+
bool CDashboard::CreatePanel(void)
{
   if(!ObjectCreate(0, m_panel_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      Print("Failed to create panel: ", GetLastError());
      return false;
   }
   
   ObjectSetInteger(0, m_panel_name, OBJPROP_XDISTANCE, m_panel_x);
   ObjectSetInteger(0, m_panel_name, OBJPROP_YDISTANCE, m_panel_y);
   ObjectSetInteger(0, m_panel_name, OBJPROP_XSIZE, m_panel_width);
   ObjectSetInteger(0, m_panel_name, OBJPROP_YSIZE, m_panel_height);
   ObjectSetInteger(0, m_panel_name, OBJPROP_BGCOLOR, m_panel_color);
   ObjectSetInteger(0, m_panel_name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, m_panel_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, m_panel_name, OBJPROP_COLOR, clrBlack);
   ObjectSetInteger(0, m_panel_name, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, m_panel_name, OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, m_panel_name, OBJPROP_BACK, false);
   ObjectSetInteger(0, m_panel_name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, m_panel_name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, m_panel_name, OBJPROP_HIDDEN, true);
   
   return true;
}

//+------------------------------------------------------------------+
//| Create labels                                                   |
//+------------------------------------------------------------------+
bool CDashboard::CreateLabels(void)
{
   int y_offset = 10;
   int line_height = 15;
   
   // Title
   CreateLabel(m_panel_name + "_Title", 10, y_offset, "Fibonacci EA Dashboard", clrYellow);
   y_offset += line_height * 2;
   
   // System info
   CreateLabel(m_panel_name + "_Symbol", 10, y_offset, "Symbol: " + m_symbol);
   y_offset += line_height;
   CreateLabel(m_panel_name + "_Time", 10, y_offset, "Time: ");
   y_offset += line_height * 2;
   
   // Trading status
   CreateLabel(m_panel_name + "_TradingStatus", 10, y_offset, "Trading Status: ");
   y_offset += line_height;
   CreateLabel(m_panel_name + "_OpenTrades", 10, y_offset, "Open Trades: ");
   y_offset += line_height * 2;
   
   // Risk info
   CreateLabel(m_panel_name + "_Equity", 10, y_offset, "Equity: ");
   y_offset += line_height;
   CreateLabel(m_panel_name + "_DailyPnL", 10, y_offset, "Daily P&L: ");
   y_offset += line_height;
   CreateLabel(m_panel_name + "_Drawdown", 10, y_offset, "Drawdown: ");
   y_offset += line_height;
   CreateLabel(m_panel_name + "_FreeMargin", 10, y_offset, "Free Margin: ");
   y_offset += line_height * 2;
   
   // Fibonacci info
   CreateLabel(m_panel_name + "_FiboStatus", 10, y_offset, "Fibonacci: ");
   y_offset += line_height;
   CreateLabel(m_panel_name + "_FiboTrend", 10, y_offset, "Trend: ");
   y_offset += line_height;
   CreateLabel(m_panel_name + "_LastSwing", 10, y_offset, "Last Swing: ");
   
   return true;
}

//+------------------------------------------------------------------+
//| Create buttons                                                  |
//+------------------------------------------------------------------+
bool CDashboard::CreateButtons(void)
{
   int button_y = m_panel_height - 80;
   int button_height = 20;
   int button_spacing = 25;
   
   CreateButton(m_btn_close_all, 10, button_y, 80, button_height, "Close All", clrRed);
   CreateButton(m_btn_enable_trading, 100, button_y, 80, button_height, "Enable", clrGreen);
   CreateButton(m_btn_disable_trading, 190, button_y, 80, button_height, "Disable", clrOrange);
   
   return true;
}

//+------------------------------------------------------------------+
//| Remove panel and all objects                                   |
//+------------------------------------------------------------------+
void CDashboard::RemovePanel(void)
{
   // Remove all objects with panel name prefix
   int total_objects = ObjectsTotal(0);
   for(int i = total_objects - 1; i >= 0; i--)
   {
      string obj_name = ObjectName(0, i);
      if(StringFind(obj_name, m_panel_name) == 0)
      {
         ObjectDelete(0, obj_name);
      }
   }
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| Update all labels                                              |
//+------------------------------------------------------------------+
void CDashboard::UpdateLabels(void)
{
   UpdateSystemInfo();
   UpdateTradeInfo();
   UpdateRiskInfo();
   UpdateFibonacciInfo();
}

//+------------------------------------------------------------------+
//| Update system information                                      |
//+------------------------------------------------------------------+
void CDashboard::UpdateSystemInfo(void)
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   string time_str = StringFormat("%02d:%02d:%02d", dt.hour, dt.min, dt.sec);
   SetLabelText(m_panel_name + "_Time", "Time: " + time_str);
}

//+------------------------------------------------------------------+
//| Update trade information                                       |
//+------------------------------------------------------------------+
void CDashboard::UpdateTradeInfo(void)
{
   if(m_trade_manager != NULL && m_risk_control != NULL)
   {
      bool trading_allowed = m_risk_control.IsTradingEnabled();
      color status_color = GetStatusColor(trading_allowed);
      string status_text = trading_allowed ? "ENABLED" : "DISABLED";
      
      SetLabelText(m_panel_name + "_TradingStatus", "Trading: " + status_text);
      ObjectSetInteger(0, m_panel_name + "_TradingStatus", OBJPROP_COLOR, status_color);
      
      int open_trades = m_trade_manager.GetOpenTradesCount();
      SetLabelText(m_panel_name + "_OpenTrades", "Open Trades: " + IntegerToString(open_trades));
   }
}

//+------------------------------------------------------------------+
//| Update risk information                                        |
//+------------------------------------------------------------------+
void CDashboard::UpdateRiskInfo(void)
{
   if(m_risk_control != NULL)
   {
      RiskStats stats = m_risk_control.GetStats();
      
      SetLabelText(m_panel_name + "_Equity", "Equity: $" + FormatDouble(stats.equity));
      
      string pnl_text = "Daily P&L: $" + FormatDouble(stats.daily_pnl);
      color pnl_color = (stats.daily_pnl >= 0) ? clrGreen : clrRed;
      SetLabelText(m_panel_name + "_DailyPnL", pnl_text);
      ObjectSetInteger(0, m_panel_name + "_DailyPnL", OBJPROP_COLOR, pnl_color);
      
      string dd_text = "Drawdown: " + FormatPercent(stats.max_drawdown);
      color dd_color = (stats.max_drawdown < 5.0) ? clrGreen : (stats.max_drawdown < 10.0) ? clrYellow : clrRed;
      SetLabelText(m_panel_name + "_Drawdown", dd_text);
      ObjectSetInteger(0, m_panel_name + "_Drawdown", OBJPROP_COLOR, dd_color);
      
      SetLabelText(m_panel_name + "_FreeMargin", "Free Margin: $" + FormatDouble(stats.free_margin));
   }
}

//+------------------------------------------------------------------+
//| Update Fibonacci information                                   |
//+------------------------------------------------------------------+
void CDashboard::UpdateFibonacciInfo(void)
{
   if(m_fibonacci != NULL)
   {
      bool has_fibo = m_fibonacci.HasValidFibonacci();
      color fibo_color = GetStatusColor(has_fibo);
      string fibo_text = has_fibo ? "ACTIVE" : "INACTIVE";
      
      SetLabelText(m_panel_name + "_FiboStatus", "Fibonacci: " + fibo_text);
      ObjectSetInteger(0, m_panel_name + "_FiboStatus", OBJPROP_COLOR, fibo_color);
      
      if(has_fibo)
      {
         string trend_text = m_fibonacci.IsUptrend() ? "UPTREND" : "DOWNTREND";
         color trend_color = m_fibonacci.IsUptrend() ? clrGreen : clrRed;
         SetLabelText(m_panel_name + "_FiboTrend", "Trend: " + trend_text);
         ObjectSetInteger(0, m_panel_name + "_FiboTrend", OBJPROP_COLOR, trend_color);
      }
      else
      {
         SetLabelText(m_panel_name + "_FiboTrend", "Trend: N/A");
         ObjectSetInteger(0, m_panel_name + "_FiboTrend", OBJPROP_COLOR, clrGray);
      }
   }
}

//+------------------------------------------------------------------+
//| Handle close all trades button                                |
//+------------------------------------------------------------------+
void CDashboard::OnCloseAllTrades(void)
{
   if(m_trade_manager != NULL)
   {
      if(m_trade_manager.CloseAllTrades())
         Print("All trades closed successfully");
      else
         Print("Failed to close some trades");
   }
   
   // Reset button state
   ObjectSetInteger(0, m_btn_close_all, OBJPROP_STATE, false);
}

//+------------------------------------------------------------------+
//| Handle enable trading button                                  |
//+------------------------------------------------------------------+
void CDashboard::OnEnableTrading(void)
{
   if(m_risk_control != NULL)
   {
      m_risk_control.EnableTrading();
      Print("Trading enabled");
   }
   
   // Reset button state
   ObjectSetInteger(0, m_btn_enable_trading, OBJPROP_STATE, false);
}

//+------------------------------------------------------------------+
//| Handle disable trading button                                 |
//+------------------------------------------------------------------+
void CDashboard::OnDisableTrading(void)
{
   if(m_risk_control != NULL)
   {
      m_risk_control.DisableTrading();
      Print("Trading disabled");
   }
   
   // Reset button state
   ObjectSetInteger(0, m_btn_disable_trading, OBJPROP_STATE, false);
}

//+------------------------------------------------------------------+
//| Create label                                                   |
//+------------------------------------------------------------------+
bool CDashboard::CreateLabel(string name, int x, int y, string text, color clr=clrWhite)
{
   if(!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
      return false;
      
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, m_panel_x + x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, m_panel_y + y);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_font_size);
   ObjectSetString(0, name, OBJPROP_FONT, m_font_name);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   
   return true;
}

//+------------------------------------------------------------------+
//| Create button                                                  |
//+------------------------------------------------------------------+
bool CDashboard::CreateButton(string name, int x, int y, int width, int height, string text, color bg_color=clrBlue)
{
   if(!ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0))
      return false;
      
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, m_panel_x + x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, m_panel_y + y);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
   ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_font_size);
   ObjectSetString(0, name, OBJPROP_FONT, m_font_name);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   
   return true;
}

//+------------------------------------------------------------------+
//| Set label text                                                 |
//+------------------------------------------------------------------+
void CDashboard::SetLabelText(string name, string text)
{
   ObjectSetString(0, name, OBJPROP_TEXT, text);
}

//+------------------------------------------------------------------+
//| Format double value                                            |
//+------------------------------------------------------------------+
string CDashboard::FormatDouble(double value, int digits=2)
{
   return DoubleToString(value, digits);
}

//+------------------------------------------------------------------+
//| Format percentage value                                        |
//+------------------------------------------------------------------+
string CDashboard::FormatPercent(double value)
{
   return DoubleToString(value, 2) + "%";
}

//+------------------------------------------------------------------+
//| Get status color                                               |
//+------------------------------------------------------------------+
color CDashboard::GetStatusColor(bool status)
{
   return status ? clrGreen : clrRed;
}
