//+------------------------------------------------------------------+
//|                                                 TradeManager.mqh |
//|                                  Copyright 2024, Fibonacci EA    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Fibonacci EA"
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>
#include "RiskControl.mqh"
#include "FibonacciEngine.mqh"

//+------------------------------------------------------------------+
//| Trade Information Structure                                      |
//+------------------------------------------------------------------+
struct TradeInfo
{
   ulong    ticket;
   double   entry_price;
   double   stop_loss;
   double   take_profit;
   double   lot_size;
   double   fib_level;
   datetime open_time;
   int      trade_type; // 0=buy, 1=sell
   bool     is_active;
};

//+------------------------------------------------------------------+
//| Trade Manager Class                                             |
//+------------------------------------------------------------------+
class CTradeManager
{
private:
   string            m_symbol;
   ENUM_TIMEFRAMES   m_timeframe;
   CTrade            m_trade;
   
   // Trade tracking
   TradeInfo         m_trades[];
   int               m_trade_count;
   
   // References to other modules
   CRiskControl*     m_risk_control;
   CFibonacciEngine* m_fibonacci;
   
   // Configuration
   int               m_magic_number;
   string            m_comment;
   ENUM_ORDER_TYPE_FILLING m_fill_type;
   
   // Trade management settings
   bool              m_use_trailing_stop;
   int               m_trailing_distance_pips;
   bool              m_use_breakeven;
   int               m_breakeven_distance_pips;
   bool              m_use_partial_close;
   double            m_partial_close_pct;
   
   bool              m_initialized;

public:
   // Constructor
   CTradeManager(void);
   ~CTradeManager(void);
   
   // Initialization
   bool              Init(string symbol, ENUM_TIMEFRAMES timeframe, int magic_number);
   void              Deinit(void);
   
   // Set references
   void              SetRiskControl(CRiskControl* risk_control) { m_risk_control = risk_control; }
   void              SetFibonacci(CFibonacciEngine* fibonacci) { m_fibonacci = fibonacci; }
   
   // Configuration
   void              SetTrailingStop(bool use_trailing, int distance_pips);
   void              SetBreakeven(bool use_breakeven, int distance_pips);
   void              SetPartialClose(bool use_partial, double close_pct);
   void              SetComment(string comment) { m_comment = comment; }
   
   // Main trading methods
   bool              OpenTrade(ENUM_ORDER_TYPE order_type, double entry_price, double stop_price, double fib_level);
   bool              CloseTrade(ulong ticket);
   bool              CloseAllTrades(void);
   
   // Trade management
   bool              Update(void);
   bool              ManageOpenTrades(void);
   bool              CheckExitConditions(void);
   
   // Information
   int               GetOpenTradesCount(void);
   TradeInfo         GetTrade(int index);
   bool              HasTradeAtLevel(double fib_level);
   
private:
   // Internal methods
   bool              ExecuteMarketOrder(ENUM_ORDER_TYPE order_type, double lot_size, double stop_loss, double take_profit, double fib_level);
   bool              ModifyTrade(ulong ticket, double new_sl, double new_tp);
   void              AddTradeInfo(ulong ticket, double entry_price, double stop_loss, double take_profit, double lot_size, double fib_level, int trade_type);
   void              RemoveTradeInfo(ulong ticket);
   int               FindTradeIndex(ulong ticket);
   
   // Trade management functions
   bool              UpdateTrailingStop(TradeInfo &trade);
   bool              UpdateBreakeven(TradeInfo &trade);
   bool              CheckPartialClose(TradeInfo &trade);
   
   // Utility functions
   double            NormalizePrice(double price);
   double            PipsToPrice(int pips);
   int               PriceToPips(double price_diff);
   bool              IsTradeInProfit(TradeInfo &trade);
   double            GetCurrentPrice(int trade_type); // 0=buy uses bid, 1=sell uses ask
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CTradeManager::CTradeManager(void)
{
   m_symbol = "";
   m_timeframe = PERIOD_CURRENT;
   m_trade_count = 0;
   m_risk_control = NULL;
   m_fibonacci = NULL;
   m_magic_number = 0;
   m_comment = "FiboEA";
   m_fill_type = ORDER_FILLING_FOK;
   
   m_use_trailing_stop = false;
   m_trailing_distance_pips = 20;
   m_use_breakeven = true;
   m_breakeven_distance_pips = 10;
   m_use_partial_close = false;
   m_partial_close_pct = 50.0;
   
   m_initialized = false;
   
   ArrayResize(m_trades, 50);
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CTradeManager::~CTradeManager(void)
{
   Deinit();
}

//+------------------------------------------------------------------+
//| Initialize trade manager                                        |
//+------------------------------------------------------------------+
bool CTradeManager::Init(string symbol, ENUM_TIMEFRAMES timeframe, int magic_number)
{
   m_symbol = symbol;
   m_timeframe = timeframe;
   m_magic_number = magic_number;
   
   // Setup trade object
   m_trade.SetExpertMagicNumber(m_magic_number);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(m_symbol);
   
   m_initialized = true;
   Print("TradeManager initialized for ", m_symbol, " with magic number ", m_magic_number);
   return true;
}

//+------------------------------------------------------------------+
//| Cleanup                                                         |
//+------------------------------------------------------------------+
void CTradeManager::Deinit(void)
{
   m_initialized = false;
   m_trade_count = 0;
}

//+------------------------------------------------------------------+
//| Set trailing stop configuration                                |
//+------------------------------------------------------------------+
void CTradeManager::SetTrailingStop(bool use_trailing, int distance_pips)
{
   m_use_trailing_stop = use_trailing;
   m_trailing_distance_pips = distance_pips;
}

//+------------------------------------------------------------------+
//| Set breakeven configuration                                    |
//+------------------------------------------------------------------+
void CTradeManager::SetBreakeven(bool use_breakeven, int distance_pips)
{
   m_use_breakeven = use_breakeven;
   m_breakeven_distance_pips = distance_pips;
}

//+------------------------------------------------------------------+
//| Set partial close configuration                                |
//+------------------------------------------------------------------+
void CTradeManager::SetPartialClose(bool use_partial, double close_pct)
{
   m_use_partial_close = use_partial;
   m_partial_close_pct = close_pct;
}

//+------------------------------------------------------------------+
//| Open new trade                                                 |
//+------------------------------------------------------------------+
bool CTradeManager::OpenTrade(ENUM_ORDER_TYPE order_type, double entry_price, double stop_price, double fib_level)
{
   if(!m_initialized || m_risk_control == NULL)
   {
      Print("TradeManager not properly initialized");
      return false;
   }
   
   // Check if trading is allowed
   if(!m_risk_control.CanOpenNewTrade())
   {
      Print("Trading not allowed: ", m_risk_control.GetLastError());
      return false;
   }
   
   // Check if we already have trade at this level
   if(HasTradeAtLevel(fib_level))
   {
      Print("Trade already exists at Fibonacci level ", fib_level);
      return false;
   }
   
   // Calculate lot size
   double lot_size = m_risk_control.CalculateLotSize(entry_price, stop_price);
   if(lot_size <= 0)
   {
      Print("Invalid lot size calculated");
      return false;
   }
   
   // Validate order
   if(!m_risk_control.ValidateOrder(lot_size, entry_price, stop_price))
   {
      Print("Order validation failed: ", m_risk_control.GetLastError());
      return false;
   }
   
   // Calculate take profit (use Fibonacci exit levels)
   double take_profit = 0;
   if(m_fibonacci != NULL)
   {
      double exit_level_price, exit_fib_level;
      if(m_fibonacci.IsPriceAtExitLevel(entry_price, exit_level_price, exit_fib_level))
      {
         take_profit = exit_level_price;
      }
   }
   
   // Execute trade
   return ExecuteMarketOrder(order_type, lot_size, stop_price, take_profit, fib_level);
}

//+------------------------------------------------------------------+
//| Close specific trade                                           |
//+------------------------------------------------------------------+
bool CTradeManager::CloseTrade(ulong ticket)
{
   if(!m_trade.PositionClose(ticket))
   {
      Print("Failed to close trade ", ticket, ": ", m_trade.ResultRetcodeDescription());
      return false;
   }
   
   RemoveTradeInfo(ticket);
   Print("Trade ", ticket, " closed successfully");
   return true;
}

//+------------------------------------------------------------------+
//| Close all trades                                               |
//+------------------------------------------------------------------+
bool CTradeManager::CloseAllTrades(void)
{
   bool all_closed = true;
   
   for(int i = m_trade_count - 1; i >= 0; i--)
   {
      if(m_trades[i].is_active)
      {
         if(!CloseTrade(m_trades[i].ticket))
            all_closed = false;
      }
   }
   
   return all_closed;
}

//+------------------------------------------------------------------+
//| Update trade management                                         |
//+------------------------------------------------------------------+
bool CTradeManager::Update(void)
{
   if(!m_initialized)
      return false;
      
   // Update trade information
   ManageOpenTrades();
   
   // Check exit conditions
   CheckExitConditions();
   
   return true;
}

//+------------------------------------------------------------------+
//| Manage open trades                                             |
//+------------------------------------------------------------------+
bool CTradeManager::ManageOpenTrades(void)
{
   for(int i = 0; i < m_trade_count; i++)
   {
      if(!m_trades[i].is_active)
         continue;
         
      // Check if position still exists
      if(!PositionSelectByTicket(m_trades[i].ticket))
      {
         m_trades[i].is_active = false;
         continue;
      }
      
      // Update trailing stop
      if(m_use_trailing_stop)
         UpdateTrailingStop(m_trades[i]);
         
      // Update breakeven
      if(m_use_breakeven)
         UpdateBreakeven(m_trades[i]);
         
      // Check partial close
      if(m_use_partial_close)
         CheckPartialClose(m_trades[i]);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Check exit conditions based on Fibonacci levels               |
//+------------------------------------------------------------------+
bool CTradeManager::CheckExitConditions(void)
{
   if(m_fibonacci == NULL || !m_fibonacci.HasValidFibonacci())
      return false;
      
   double current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   double exit_level_price, exit_fib_level;
   
   // Check if price reached exit level
   if(m_fibonacci.IsPriceAtExitLevel(current_price, exit_level_price, exit_fib_level))
   {
      // Close trades that should exit at this level
      for(int i = 0; i < m_trade_count; i++)
      {
         if(m_trades[i].is_active)
         {
            // Logic to determine if this trade should be closed
            // This could be based on trade direction and Fibonacci level
            bool should_close = false;
            
            if(m_fibonacci.IsUptrend() && m_trades[i].trade_type == 0) // Buy trade in uptrend
               should_close = true;
            else if(!m_fibonacci.IsUptrend() && m_trades[i].trade_type == 1) // Sell trade in downtrend
               should_close = true;
               
            if(should_close)
            {
               CloseTrade(m_trades[i].ticket);
               Print("Trade closed at Fibonacci exit level ", exit_fib_level);
            }
         }
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Get number of open trades                                      |
//+------------------------------------------------------------------+
int CTradeManager::GetOpenTradesCount(void)
{
   int count = 0;
   for(int i = 0; i < m_trade_count; i++)
   {
      if(m_trades[i].is_active)
         count++;
   }
   return count;
}

//+------------------------------------------------------------------+
//| Get trade information by index                                 |
//+------------------------------------------------------------------+
TradeInfo CTradeManager::GetTrade(int index)
{
   TradeInfo empty_trade = {0, 0, 0, 0, 0, 0, 0, 0, false};
   
   if(index >= 0 && index < m_trade_count)
      return m_trades[index];
      
   return empty_trade;
}

//+------------------------------------------------------------------+
//| Check if trade exists at Fibonacci level                      |
//+------------------------------------------------------------------+
bool CTradeManager::HasTradeAtLevel(double fib_level)
{
   for(int i = 0; i < m_trade_count; i++)
   {
      if(m_trades[i].is_active && MathAbs(m_trades[i].fib_level - fib_level) < 0.001)
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Execute market order                                           |
//+------------------------------------------------------------------+
bool CTradeManager::ExecuteMarketOrder(ENUM_ORDER_TYPE order_type, double lot_size, double stop_loss, double take_profit, double fib_level)
{
   double price = (order_type == ORDER_TYPE_BUY) ? SymbolInfoDouble(m_symbol, SYMBOL_ASK) : SymbolInfoDouble(m_symbol, SYMBOL_BID);
   
   // Normalize prices
   price = NormalizePrice(price);
   stop_loss = NormalizePrice(stop_loss);
   if(take_profit > 0)
      take_profit = NormalizePrice(take_profit);
   
   // Execute order
   bool result = false;
   if(order_type == ORDER_TYPE_BUY)
      result = m_trade.Buy(lot_size, m_symbol, price, stop_loss, take_profit, m_comment);
   else
      result = m_trade.Sell(lot_size, m_symbol, price, stop_loss, take_profit, m_comment);
   
   if(!result)
   {
      Print("Failed to execute order: ", m_trade.ResultRetcodeDescription());
      return false;
   }
   
   // Add trade info
   ulong ticket = m_trade.ResultOrder();
   int trade_type = (order_type == ORDER_TYPE_BUY) ? 0 : 1;
   AddTradeInfo(ticket, price, stop_loss, take_profit, lot_size, fib_level, trade_type);
   
   Print("Trade opened: Ticket=", ticket, " Type=", EnumToString(order_type), " Lots=", lot_size, " FibLevel=", fib_level);
   return true;
}

//+------------------------------------------------------------------+
//| Add trade information                                          |
//+------------------------------------------------------------------+
void CTradeManager::AddTradeInfo(ulong ticket, double entry_price, double stop_loss, double take_profit, double lot_size, double fib_level, int trade_type)
{
   if(m_trade_count >= ArraySize(m_trades))
   {
      ArrayResize(m_trades, ArraySize(m_trades) + 20);
   }
   
   m_trades[m_trade_count].ticket = ticket;
   m_trades[m_trade_count].entry_price = entry_price;
   m_trades[m_trade_count].stop_loss = stop_loss;
   m_trades[m_trade_count].take_profit = take_profit;
   m_trades[m_trade_count].lot_size = lot_size;
   m_trades[m_trade_count].fib_level = fib_level;
   m_trades[m_trade_count].open_time = TimeCurrent();
   m_trades[m_trade_count].trade_type = trade_type;
   m_trades[m_trade_count].is_active = true;
   
   m_trade_count++;
}

//+------------------------------------------------------------------+
//| Remove trade information                                       |
//+------------------------------------------------------------------+
void CTradeManager::RemoveTradeInfo(ulong ticket)
{
   int index = FindTradeIndex(ticket);
   if(index >= 0)
      m_trades[index].is_active = false;
}

//+------------------------------------------------------------------+
//| Find trade index by ticket                                     |
//+------------------------------------------------------------------+
int CTradeManager::FindTradeIndex(ulong ticket)
{
   for(int i = 0; i < m_trade_count; i++)
   {
      if(m_trades[i].ticket == ticket)
         return i;
   }
   return -1;
}

//+------------------------------------------------------------------+
//| Normalize price to symbol digits                               |
//+------------------------------------------------------------------+
double CTradeManager::NormalizePrice(double price)
{
   return NormalizeDouble(price, (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS));
}

//+------------------------------------------------------------------+
//| Convert pips to price                                          |
//+------------------------------------------------------------------+
double CTradeManager::PipsToPrice(int pips)
{
   double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS);
   
   if(digits == 3 || digits == 5)
      return pips * point * 10;
   else
      return pips * point;
}

//+------------------------------------------------------------------+
//| Update trailing stop for trade                                |
//+------------------------------------------------------------------+
bool CTradeManager::UpdateTrailingStop(TradeInfo &trade)
{
   if(!PositionSelectByTicket(trade.ticket))
      return false;
      
   double current_price = GetCurrentPrice(trade.trade_type);
   double new_sl = trade.stop_loss;
   double trailing_distance = PipsToPrice(m_trailing_distance_pips);
   
   if(trade.trade_type == 0) // Buy trade
   {
      double potential_sl = current_price - trailing_distance;
      if(potential_sl > trade.stop_loss)
         new_sl = potential_sl;
   }
   else // Sell trade
   {
      double potential_sl = current_price + trailing_distance;
      if(potential_sl < trade.stop_loss)
         new_sl = potential_sl;
   }
   
   if(new_sl != trade.stop_loss)
   {
      if(ModifyTrade(trade.ticket, new_sl, trade.take_profit))
      {
         trade.stop_loss = new_sl;
         return true;
      }
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Update breakeven for trade                                     |
//+------------------------------------------------------------------+
bool CTradeManager::UpdateBreakeven(TradeInfo &trade)
{
   if(!IsTradeInProfit(trade))
      return false;
      
   double current_price = GetCurrentPrice(trade.trade_type);
   double breakeven_distance = PipsToPrice(m_breakeven_distance_pips);
   
   bool should_move_to_breakeven = false;
   
   if(trade.trade_type == 0) // Buy trade
   {
      if(current_price >= trade.entry_price + breakeven_distance)
         should_move_to_breakeven = true;
   }
   else // Sell trade
   {
      if(current_price <= trade.entry_price - breakeven_distance)
         should_move_to_breakeven = true;
   }
   
   if(should_move_to_breakeven && trade.stop_loss != trade.entry_price)
   {
      if(ModifyTrade(trade.ticket, trade.entry_price, trade.take_profit))
      {
         trade.stop_loss = trade.entry_price;
         Print("Trade ", trade.ticket, " moved to breakeven");
         return true;
      }
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Check if trade is in profit                                   |
//+------------------------------------------------------------------+
bool CTradeManager::IsTradeInProfit(TradeInfo &trade)
{
   if(!PositionSelectByTicket(trade.ticket))
      return false;
      
   return PositionGetDouble(POSITION_PROFIT) > 0;
}

//+------------------------------------------------------------------+
//| Get current price for trade type                              |
//+------------------------------------------------------------------+
double CTradeManager::GetCurrentPrice(int trade_type)
{
   if(trade_type == 0) // Buy trade uses bid for closing
      return SymbolInfoDouble(m_symbol, SYMBOL_BID);
   else // Sell trade uses ask for closing
      return SymbolInfoDouble(m_symbol, SYMBOL_ASK);
}

//+------------------------------------------------------------------+
//| Modify trade stop loss and take profit                        |
//+------------------------------------------------------------------+
bool CTradeManager::ModifyTrade(ulong ticket, double new_sl, double new_tp)
{
   new_sl = NormalizePrice(new_sl);
   if(new_tp > 0)
      new_tp = NormalizePrice(new_tp);
      
   return m_trade.PositionModify(ticket, new_sl, new_tp);
}

//+------------------------------------------------------------------+
//| Check partial close conditions                                |
//+------------------------------------------------------------------+
bool CTradeManager::CheckPartialClose(TradeInfo &trade)
{
   // Implementation for partial close logic
   // This would check if trade has reached certain profit levels
   // and close a percentage of the position
   return false; // Placeholder
}
