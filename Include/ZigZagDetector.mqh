//+------------------------------------------------------------------+
//|                                               ZigZagDetector.mqh |
//|                                  Copyright 2024, Fibonacci EA    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Fibonacci EA"
#property link      "https://www.mql5.com"
#property version   "1.00"

//+------------------------------------------------------------------+
//| ZigZag Swing Point Structure                                     |
//+------------------------------------------------------------------+
struct <PERSON>oint
{
   datetime time;
   double   price;
   int      type;     // 1 = High, -1 = Low
   int      bar_index;
};

//+------------------------------------------------------------------+
//| ZigZag Detector Class                                            |
//+------------------------------------------------------------------+
class CZigZagDetector
{
private:
   int               m_depth;
   int               m_deviation;
   int               m_backstep;
   string            m_symbol;
   ENUM_TIMEFRAMES   m_timeframe;

   // ZigZag indicator handle
   int               m_zigzag_handle;

   SwingPoint        m_swings[];
   int               m_swing_count;
   int               m_last_swing_count;

   bool              m_initialized;

public:
   // Constructor
   CZigZagDetector(void);
   ~CZigZagDetector(void);

   // Initialization
   bool              Init(string symbol, ENUM_TIMEFRAMES timeframe, int depth=12, int deviation=5, int backstep=3);
   void              Deinit(void);

   // Main methods
   bool              Update(void);
   bool              HasNewSwing(void);

   // Getters
   SwingPoint        GetLastSwing(void);
   SwingPoint        GetSwing(int index);
   int               GetSwingCount(void) { return m_swing_count; }

   bool              GetLastTwoSwings(SwingPoint &swing1, SwingPoint &swing2);
   bool              IsValidSwingPattern(void);

   // Debug methods
   void              PrintSwings(void);
   void              PrintLastSwing(void);

private:
   // Internal methods
   bool              ScanZigZagBuffer(void);
   void              AddSwing(datetime time, double price, int type, int bar_index);
   bool              IsNewSwingPoint(double price, datetime time, int bar_index);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CZigZagDetector::CZigZagDetector(void)
{
   m_depth = 12;
   m_deviation = 5;
   m_backstep = 3;
   m_symbol = "";
   m_timeframe = PERIOD_CURRENT;
   m_swing_count = 0;
   m_last_swing_count = 0;
   m_zigzag_handle = INVALID_HANDLE;
   m_initialized = false;

   ArrayResize(m_swings, 100);
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CZigZagDetector::~CZigZagDetector(void)
{
   Deinit();
}

//+------------------------------------------------------------------+
//| Initialize ZigZag detector                                      |
//+------------------------------------------------------------------+
bool CZigZagDetector::Init(string symbol, ENUM_TIMEFRAMES timeframe, int depth=12, int deviation=5, int backstep=3)
{
   m_symbol = symbol;
   m_timeframe = timeframe;
   m_depth = depth;
   m_deviation = deviation;
   m_backstep = backstep;

   if(m_depth < 1 || m_deviation < 1 || m_backstep < 1)
   {
      Print("ZigZagDetector: Invalid parameters");
      return false;
   }

   // Create ZigZag indicator handle
   m_zigzag_handle = iCustom(m_symbol, m_timeframe, "Examples\\ZigZag", m_depth, m_deviation, m_backstep);
   if(m_zigzag_handle == INVALID_HANDLE)
   {
      Print("ZigZagDetector: Failed to create ZigZag indicator handle");
      return false;
   }

   // Wait for indicator to calculate
   Sleep(100);

   // Initial scan for existing swings
   if(!ScanZigZagBuffer())
   {
      Print("ZigZagDetector: Failed to scan initial ZigZag buffer");
      return false;
   }

   m_initialized = true;
   Print("ZigZagDetector initialized for ", m_symbol, " ", EnumToString(m_timeframe));
   Print("ZigZag parameters: Depth=", m_depth, " Deviation=", m_deviation, " Backstep=", m_backstep);
   Print("Initial swing count: ", m_swing_count);

   return true;
}

//+------------------------------------------------------------------+
//| Cleanup                                                         |
//+------------------------------------------------------------------+
void CZigZagDetector::Deinit(void)
{
   if(m_zigzag_handle != INVALID_HANDLE)
   {
      IndicatorRelease(m_zigzag_handle);
      m_zigzag_handle = INVALID_HANDLE;
   }

   m_initialized = false;
   m_swing_count = 0;
   m_last_swing_count = 0;
}

//+------------------------------------------------------------------+
//| Update ZigZag and detect new swings                            |
//+------------------------------------------------------------------+
bool CZigZagDetector::Update(void)
{
   if(!m_initialized || m_zigzag_handle == INVALID_HANDLE)
      return false;

   return ScanZigZagBuffer();
}

//+------------------------------------------------------------------+
//| Check if there's a new confirmed swing                         |
//+------------------------------------------------------------------+
bool CZigZagDetector::HasNewSwing(void)
{
   if(m_swing_count > m_last_swing_count)
   {
      m_last_swing_count = m_swing_count;
      Print("ZigZagDetector: New swing detected! Total swings: ", m_swing_count);
      PrintLastSwing();
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Get the last swing point                                       |
//+------------------------------------------------------------------+
SwingPoint CZigZagDetector::GetLastSwing(void)
{
   SwingPoint empty_swing = {0, 0, 0, -1};
   
   if(m_swing_count > 0)
      return m_swings[m_swing_count - 1];
      
   return empty_swing;
}

//+------------------------------------------------------------------+
//| Get swing by index (0 = oldest, count-1 = newest)             |
//+------------------------------------------------------------------+
SwingPoint CZigZagDetector::GetSwing(int index)
{
   SwingPoint empty_swing = {0, 0, 0, -1};
   
   if(index >= 0 && index < m_swing_count)
      return m_swings[index];
      
   return empty_swing;
}

//+------------------------------------------------------------------+
//| Get last two swings for Fibonacci calculation                  |
//+------------------------------------------------------------------+
bool CZigZagDetector::GetLastTwoSwings(SwingPoint &swing1, SwingPoint &swing2)
{
   if(m_swing_count < 2)
      return false;
      
   swing1 = m_swings[m_swing_count - 2]; // Second to last
   swing2 = m_swings[m_swing_count - 1]; // Last
   
   return true;
}

//+------------------------------------------------------------------+
//| Check if we have a valid swing pattern                         |
//+------------------------------------------------------------------+
bool CZigZagDetector::IsValidSwingPattern(void)
{
   if(m_swing_count < 2)
      return false;
      
   SwingPoint swing1 = m_swings[m_swing_count - 2];
   SwingPoint swing2 = m_swings[m_swing_count - 1];
   
   // Swings should be of opposite types
   return (swing1.type != swing2.type);
}

//+------------------------------------------------------------------+
//| Scan ZigZag indicator buffer for swing points                  |
//+------------------------------------------------------------------+
bool CZigZagDetector::ScanZigZagBuffer(void)
{
   if(m_zigzag_handle == INVALID_HANDLE)
      return false;

   int bars = iBars(m_symbol, m_timeframe);
   if(bars < 100)
      return false;

   // Get ZigZag buffer values (scan last 500 bars)
   int scan_bars = MathMin(500, bars);
   double zigzag_buffer[];
   ArraySetAsSeries(zigzag_buffer, true);

   if(CopyBuffer(m_zigzag_handle, 0, 0, scan_bars, zigzag_buffer) <= 0)
   {
      Print("ZigZagDetector: Failed to copy ZigZag buffer, error: ", GetLastError());
      return false;
   }

   // Clear existing swings
   m_swing_count = 0;

   // Scan for non-zero values (swing points)
   for(int i = 0; i < scan_bars; i++)
   {
      if(zigzag_buffer[i] != 0.0 && zigzag_buffer[i] != EMPTY_VALUE)
      {
         datetime time = iTime(m_symbol, m_timeframe, i);
         double high = iHigh(m_symbol, m_timeframe, i);
         double low = iLow(m_symbol, m_timeframe, i);

         // Determine if it's a high or low swing
         int swing_type = 0;
         if(MathAbs(zigzag_buffer[i] - high) < MathAbs(zigzag_buffer[i] - low))
            swing_type = 1;  // High swing
         else
            swing_type = -1; // Low swing

         if(IsNewSwingPoint(zigzag_buffer[i], time, i))
         {
            AddSwing(time, zigzag_buffer[i], swing_type, i);
         }
      }
   }

   // Sort swings by time (oldest first)
   for(int i = 0; i < m_swing_count - 1; i++)
   {
      for(int j = i + 1; j < m_swing_count; j++)
      {
         if(m_swings[i].time > m_swings[j].time)
         {
            SwingPoint temp = m_swings[i];
            m_swings[i] = m_swings[j];
            m_swings[j] = temp;
         }
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Check if this is a new swing point                             |
//+------------------------------------------------------------------+
bool CZigZagDetector::IsNewSwingPoint(double price, datetime time, int bar_index)
{
   // Check if we already have this swing point
   for(int i = 0; i < m_swing_count; i++)
   {
      if(MathAbs(m_swings[i].price - price) < 0.00001 && m_swings[i].time == time)
         return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Print all swings for debugging                                 |
//+------------------------------------------------------------------+
void CZigZagDetector::PrintSwings(void)
{
   Print("=== ZigZag Swings (Total: ", m_swing_count, ") ===");
   for(int i = 0; i < m_swing_count; i++)
   {
      string type_str = (m_swings[i].type == 1) ? "HIGH" : "LOW";
      Print("Swing ", i, ": ", type_str, " at ", DoubleToString(m_swings[i].price, Digits()),
            " | Time: ", TimeToString(m_swings[i].time), " | Bar: ", m_swings[i].bar_index);
   }
   Print("=====================================");
}

//+------------------------------------------------------------------+
//| Print last swing for debugging                                 |
//+------------------------------------------------------------------+
void CZigZagDetector::PrintLastSwing(void)
{
   if(m_swing_count > 0)
   {
      SwingPoint last = m_swings[m_swing_count - 1];
      string type_str = (last.type == 1) ? "HIGH" : "LOW";
      Print("Last Swing: ", type_str, " at ", DoubleToString(last.price, Digits()),
            " | Time: ", TimeToString(last.time), " | Bar: ", last.bar_index);
   }
   else
   {
      Print("No swings detected yet");
   }
}

//+------------------------------------------------------------------+
//| Add new swing point                                            |
//+------------------------------------------------------------------+
void CZigZagDetector::AddSwing(datetime time, double price, int type, int bar_index)
{
   if(m_swing_count >= ArraySize(m_swings))
   {
      ArrayResize(m_swings, ArraySize(m_swings) + 50);
   }

   m_swings[m_swing_count].time = time;
   m_swings[m_swing_count].price = price;
   m_swings[m_swing_count].type = type;
   m_swings[m_swing_count].bar_index = bar_index;

   m_swing_count++;

   // Debug output
   string type_str = (type == 1) ? "HIGH" : "LOW";
   Print("Added swing: ", type_str, " at ", DoubleToString(price, Digits()),
         " | Time: ", TimeToString(time), " | Bar: ", bar_index);
}
