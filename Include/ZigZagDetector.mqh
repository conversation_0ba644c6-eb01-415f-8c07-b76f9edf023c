//+------------------------------------------------------------------+
//|                                               ZigZagDetector.mqh |
//|                                  Copyright 2024, Fibonacci EA    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Fibonacci EA"
#property link      "https://www.mql5.com"
#property version   "1.00"

//+------------------------------------------------------------------+
//| ZigZag Swing Point Structure                                     |
//+------------------------------------------------------------------+
struct <PERSON>oint
{
   datetime time;
   double   price;
   int      type;     // 1 = High, -1 = Low
   int      bar_index;
};

//+------------------------------------------------------------------+
//| ZigZag Detector Class                                            |
//+------------------------------------------------------------------+
class CZigZagDetector
{
private:
   int               m_depth;
   int               m_deviation;
   int               m_backstep;
   string            m_symbol;
   ENUM_TIMEFRAMES   m_timeframe;
   
   SwingPoint        m_swings[];
   int               m_swing_count;
   
   double            m_last_high;
   double            m_last_low;
   int               m_last_high_pos;
   int               m_last_low_pos;
   
   bool              m_initialized;

public:
   // Constructor
   CZigZagDetector(void);
   ~CZigZagDetector(void);
   
   // Initialization
   bool              Init(string symbol, ENUM_TIMEFRAMES timeframe, int depth=12, int deviation=5, int backstep=3);
   void              Deinit(void);
   
   // Main methods
   bool              Update(void);
   bool              HasNewSwing(void);
   
   // Getters
   SwingPoint        GetLastSwing(void);
   SwingPoint        GetSwing(int index);
   int               GetSwingCount(void) { return m_swing_count; }
   
   bool              GetLastTwoSwings(SwingPoint &swing1, SwingPoint &swing2);
   bool              IsValidSwingPattern(void);
   
private:
   // Internal methods
   bool              FindSwings(void);
   bool              IsNewHigh(int pos, double price);
   bool              IsNewLow(int pos, double price);
   void              AddSwing(datetime time, double price, int type, int bar_index);
   double            GetPrice(int shift, int price_type); // 0=low, 1=high
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CZigZagDetector::CZigZagDetector(void)
{
   m_depth = 12;
   m_deviation = 5;
   m_backstep = 3;
   m_symbol = "";
   m_timeframe = PERIOD_CURRENT;
   m_swing_count = 0;
   m_last_high = 0;
   m_last_low = 0;
   m_last_high_pos = -1;
   m_last_low_pos = -1;
   m_initialized = false;
   
   ArrayResize(m_swings, 100);
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CZigZagDetector::~CZigZagDetector(void)
{
   Deinit();
}

//+------------------------------------------------------------------+
//| Initialize ZigZag detector                                      |
//+------------------------------------------------------------------+
bool CZigZagDetector::Init(string symbol, ENUM_TIMEFRAMES timeframe, int depth=12, int deviation=5, int backstep=3)
{
   m_symbol = symbol;
   m_timeframe = timeframe;
   m_depth = depth;
   m_deviation = deviation;
   m_backstep = backstep;
   
   if(m_depth < 1 || m_deviation < 1 || m_backstep < 1)
   {
      Print("ZigZagDetector: Invalid parameters");
      return false;
   }
   
   // Initial scan for existing swings
   if(!FindSwings())
   {
      Print("ZigZagDetector: Failed to find initial swings");
      return false;
   }
   
   m_initialized = true;
   Print("ZigZagDetector initialized for ", m_symbol, " ", EnumToString(m_timeframe));
   return true;
}

//+------------------------------------------------------------------+
//| Cleanup                                                         |
//+------------------------------------------------------------------+
void CZigZagDetector::Deinit(void)
{
   m_initialized = false;
   m_swing_count = 0;
}

//+------------------------------------------------------------------+
//| Update ZigZag and detect new swings                            |
//+------------------------------------------------------------------+
bool CZigZagDetector::Update(void)
{
   if(!m_initialized)
      return false;
      
   return FindSwings();
}

//+------------------------------------------------------------------+
//| Check if there's a new confirmed swing                         |
//+------------------------------------------------------------------+
bool CZigZagDetector::HasNewSwing(void)
{
   static int last_count = 0;
   
   if(m_swing_count > last_count)
   {
      last_count = m_swing_count;
      return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Get the last swing point                                       |
//+------------------------------------------------------------------+
SwingPoint CZigZagDetector::GetLastSwing(void)
{
   SwingPoint empty_swing = {0, 0, 0, -1};
   
   if(m_swing_count > 0)
      return m_swings[m_swing_count - 1];
      
   return empty_swing;
}

//+------------------------------------------------------------------+
//| Get swing by index (0 = oldest, count-1 = newest)             |
//+------------------------------------------------------------------+
SwingPoint CZigZagDetector::GetSwing(int index)
{
   SwingPoint empty_swing = {0, 0, 0, -1};
   
   if(index >= 0 && index < m_swing_count)
      return m_swings[index];
      
   return empty_swing;
}

//+------------------------------------------------------------------+
//| Get last two swings for Fibonacci calculation                  |
//+------------------------------------------------------------------+
bool CZigZagDetector::GetLastTwoSwings(SwingPoint &swing1, SwingPoint &swing2)
{
   if(m_swing_count < 2)
      return false;
      
   swing1 = m_swings[m_swing_count - 2]; // Second to last
   swing2 = m_swings[m_swing_count - 1]; // Last
   
   return true;
}

//+------------------------------------------------------------------+
//| Check if we have a valid swing pattern                         |
//+------------------------------------------------------------------+
bool CZigZagDetector::IsValidSwingPattern(void)
{
   if(m_swing_count < 2)
      return false;
      
   SwingPoint swing1 = m_swings[m_swing_count - 2];
   SwingPoint swing2 = m_swings[m_swing_count - 1];
   
   // Swings should be of opposite types
   return (swing1.type != swing2.type);
}

//+------------------------------------------------------------------+
//| Find swings using ZigZag algorithm                             |
//+------------------------------------------------------------------+
bool CZigZagDetector::FindSwings(void)
{
   int bars = iBars(m_symbol, m_timeframe);
   if(bars < m_depth * 2)
      return false;
      
   double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS);
   
   // Scan for new highs and lows
   for(int i = m_depth; i < bars - m_depth; i++)
   {
      double high = GetPrice(i, 1);
      double low = GetPrice(i, 0);
      
      // Check for new high
      if(IsNewHigh(i, high))
      {
         datetime time = iTime(m_symbol, m_timeframe, i);
         AddSwing(time, high, 1, i);
      }
      
      // Check for new low  
      if(IsNewLow(i, low))
      {
         datetime time = iTime(m_symbol, m_timeframe, i);
         AddSwing(time, low, -1, i);
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Check if price forms a new high                                |
//+------------------------------------------------------------------+
bool CZigZagDetector::IsNewHigh(int pos, double price)
{
   // Check if this is highest in depth range
   for(int i = pos - m_depth; i <= pos + m_depth; i++)
   {
      if(i == pos || i < 0) continue;
      
      double compare_price = GetPrice(i, 1);
      if(compare_price >= price)
         return false;
   }
   
   // Check deviation
   if(m_last_high > 0)
   {
      double deviation = MathAbs(price - m_last_high) / SymbolInfoDouble(m_symbol, SYMBOL_POINT);
      if(deviation < m_deviation)
         return false;
   }
   
   // Check backstep
   if(m_last_high_pos >= 0 && pos - m_last_high_pos < m_backstep)
      return false;
      
   m_last_high = price;
   m_last_high_pos = pos;
   
   return true;
}

//+------------------------------------------------------------------+
//| Check if price forms a new low                                 |
//+------------------------------------------------------------------+
bool CZigZagDetector::IsNewLow(int pos, double price)
{
   // Check if this is lowest in depth range
   for(int i = pos - m_depth; i <= pos + m_depth; i++)
   {
      if(i == pos || i < 0) continue;
      
      double compare_price = GetPrice(i, 0);
      if(compare_price <= price)
         return false;
   }
   
   // Check deviation
   if(m_last_low > 0)
   {
      double deviation = MathAbs(price - m_last_low) / SymbolInfoDouble(m_symbol, SYMBOL_POINT);
      if(deviation < m_deviation)
         return false;
   }
   
   // Check backstep
   if(m_last_low_pos >= 0 && pos - m_last_low_pos < m_backstep)
      return false;
      
   m_last_low = price;
   m_last_low_pos = pos;
   
   return true;
}

//+------------------------------------------------------------------+
//| Add new swing point                                            |
//+------------------------------------------------------------------+
void CZigZagDetector::AddSwing(datetime time, double price, int type, int bar_index)
{
   if(m_swing_count >= ArraySize(m_swings))
   {
      ArrayResize(m_swings, ArraySize(m_swings) + 50);
   }
   
   m_swings[m_swing_count].time = time;
   m_swings[m_swing_count].price = price;
   m_swings[m_swing_count].type = type;
   m_swings[m_swing_count].bar_index = bar_index;
   
   m_swing_count++;
}

//+------------------------------------------------------------------+
//| Get price (0=low, 1=high)                                      |
//+------------------------------------------------------------------+
double CZigZagDetector::GetPrice(int shift, int price_type)
{
   if(price_type == 0)
      return iLow(m_symbol, m_timeframe, shift);
   else
      return iHigh(m_symbol, m_timeframe, shift);
}
