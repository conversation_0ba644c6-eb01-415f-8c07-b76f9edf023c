//+------------------------------------------------------------------+
//|                                              FibonacciEngine.mqh |
//|                                  Copyright 2024, Fibonacci EA    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Fibonacci EA"
#property link      "https://www.mql5.com"
#property version   "1.00"

#include "ZigZagDetector.mqh"

//+------------------------------------------------------------------+
//| Fibonacci Level Structure                                        |
//+------------------------------------------------------------------+
struct FibLevel
{
   double level;
   double price;
   string name;
   bool   is_entry;
   bool   is_exit;
   bool   is_stop;
};

//+------------------------------------------------------------------+
//| Fibonacci Engine Class                                          |
//+------------------------------------------------------------------+
class CFibonacciEngine
{
private:
   string            m_symbol;
   ENUM_TIMEFRAMES   m_timeframe;
   
   // Fibonacci object
   string            m_fibo_name;
   bool              m_fibo_exists;
   
   // Current Fibonacci data
   SwingPoint        m_swing_start;
   SwingPoint        m_swing_end;
   bool              m_is_uptrend;
   
   // Fibonacci levels
   FibLevel          m_levels[];
   int               m_level_count;
   
   // Configuration
   double            m_entry_levels[];
   double            m_exit_levels[];
   double            m_stop_level;
   int               m_level_offset_pips;
   
   bool              m_initialized;

public:
   // Constructor
   CFibonacciEngine(void);
   ~CFibonacciEngine(void);
   
   // Initialization
   bool              Init(string symbol, ENUM_TIMEFRAMES timeframe);
   void              Deinit(void);
   
   // Configuration
   void              SetEntryLevels(double &levels[]);
   void              SetExitLevels(double &levels[]);
   void              SetStopLevel(double level) { m_stop_level = level; }
   void              SetLevelOffset(int pips) { m_level_offset_pips = pips; }
   
   // Main methods
   bool              UpdateFibonacci(SwingPoint &swing1, SwingPoint &swing2);
   bool              DrawFibonacci(void);
   void              RemoveFibonacci(void);
   
   // Level checking
   bool              IsPriceAtLevel(double price, double fib_level, double &level_price);
   bool              IsPriceAtEntryLevel(double price, double &level_price, double &fib_level);
   bool              IsPriceAtExitLevel(double price, double &level_price, double &fib_level);
   bool              IsPriceAtStopLevel(double price, double &level_price);
   
   // Getters
   bool              IsUptrend(void) { return m_is_uptrend; }
   bool              HasValidFibonacci(void) { return m_fibo_exists; }
   double            GetLevelPrice(double fib_level);
   FibLevel          GetLevel(int index);
   int               GetLevelCount(void) { return m_level_count; }

   // Debug methods
   void              PrintFibonacciLevels(void);
   void              DrawSwingPoints(void);
   
private:
   // Internal methods
   void              CalculateLevels(void);
   double            CalculateLevelPrice(double fib_level);
   void              CreateFibLevel(double level, string name, bool is_entry, bool is_exit, bool is_stop);
   double            PipsToPrice(int pips);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFibonacciEngine::CFibonacciEngine(void)
{
   m_symbol = "";
   m_timeframe = PERIOD_CURRENT;
   m_fibo_name = "EA_Fibonacci_" + IntegerToString(GetTickCount());
   m_fibo_exists = false;
   m_is_uptrend = false;
   m_level_count = 0;
   m_stop_level = 0.764;
   m_level_offset_pips = 2;
   m_initialized = false;
   
   ArrayResize(m_levels, 20);
   ArrayResize(m_entry_levels, 10);
   ArrayResize(m_exit_levels, 10);
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CFibonacciEngine::~CFibonacciEngine(void)
{
   Deinit();
}

//+------------------------------------------------------------------+
//| Initialize Fibonacci engine                                     |
//+------------------------------------------------------------------+
bool CFibonacciEngine::Init(string symbol, ENUM_TIMEFRAMES timeframe)
{
   m_symbol = symbol;
   m_timeframe = timeframe;
   
   // Set default levels
   double default_entry[] = {0.382, 0.5, 0.618};
   double default_exit[] = {0.0, -0.236};
   
   SetEntryLevels(default_entry);
   SetExitLevels(default_exit);
   
   m_initialized = true;
   Print("FibonacciEngine initialized for ", m_symbol, " ", EnumToString(m_timeframe));
   return true;
}

//+------------------------------------------------------------------+
//| Cleanup                                                         |
//+------------------------------------------------------------------+
void CFibonacciEngine::Deinit(void)
{
   RemoveFibonacci();
   m_initialized = false;
}

//+------------------------------------------------------------------+
//| Set entry levels                                               |
//+------------------------------------------------------------------+
void CFibonacciEngine::SetEntryLevels(double &levels[])
{
   int size = ArraySize(levels);
   ArrayResize(m_entry_levels, size);
   ArrayCopy(m_entry_levels, levels);
}

//+------------------------------------------------------------------+
//| Set exit levels                                                |
//+------------------------------------------------------------------+
void CFibonacciEngine::SetExitLevels(double &levels[])
{
   int size = ArraySize(levels);
   ArrayResize(m_exit_levels, size);
   ArrayCopy(m_exit_levels, levels);
}

//+------------------------------------------------------------------+
//| Update Fibonacci based on new swings                           |
//+------------------------------------------------------------------+
bool CFibonacciEngine::UpdateFibonacci(SwingPoint &swing1, SwingPoint &swing2)
{
   if(!m_initialized)
      return false;
      
   // Store swing points
   m_swing_start = swing1;
   m_swing_end = swing2;
   
   // Determine trend direction
   m_is_uptrend = (swing2.price > swing1.price);
   
   // Calculate all levels
   CalculateLevels();
   
   // Draw Fibonacci on chart
   if(!DrawFibonacci())
   {
      Print("Failed to draw Fibonacci");
      return false;
   }

   // Draw swing points on chart for debugging
   DrawSwingPoints();

   m_fibo_exists = true;

   Print("Fibonacci updated: ", m_is_uptrend ? "Uptrend" : "Downtrend",
         " from ", DoubleToString(swing1.price, Digits()),
         " to ", DoubleToString(swing2.price, Digits()));

   // Print Fibonacci levels
   PrintFibonacciLevels();

   return true;
}

//+------------------------------------------------------------------+
//| Draw Fibonacci retracement on chart                            |
//+------------------------------------------------------------------+
bool CFibonacciEngine::DrawFibonacci(void)
{
   // Remove existing Fibonacci
   RemoveFibonacci();
   
   // Create Fibonacci retracement object
   if(!ObjectCreate(0, m_fibo_name, OBJ_FIBO, 0, m_swing_start.time, m_swing_start.price, m_swing_end.time, m_swing_end.price))
   {
      Print("Failed to create Fibonacci object: ", GetLastError());
      return false;
   }
   
   // Set Fibonacci properties
   ObjectSetInteger(0, m_fibo_name, OBJPROP_COLOR, clrBlue);
   ObjectSetInteger(0, m_fibo_name, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, m_fibo_name, OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, m_fibo_name, OBJPROP_BACK, false);
   ObjectSetInteger(0, m_fibo_name, OBJPROP_SELECTABLE, true);
   ObjectSetInteger(0, m_fibo_name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, m_fibo_name, OBJPROP_HIDDEN, false);
   
   // Set Fibonacci levels
   ObjectSetInteger(0, m_fibo_name, OBJPROP_LEVELS, m_level_count);
   
   for(int i = 0; i < m_level_count; i++)
   {
      ObjectSetDouble(0, m_fibo_name, OBJPROP_LEVELVALUE, i, m_levels[i].level);
      ObjectSetString(0, m_fibo_name, OBJPROP_LEVELTEXT, i, m_levels[i].name + " (" + DoubleToString(m_levels[i].price, Digits()) + ")");
      
      // Color coding
      color level_color = clrGray;
      if(m_levels[i].is_entry) level_color = clrGreen;
      else if(m_levels[i].is_exit) level_color = clrBlue;
      else if(m_levels[i].is_stop) level_color = clrRed;
      
      ObjectSetInteger(0, m_fibo_name, OBJPROP_LEVELCOLOR, i, level_color);
      ObjectSetInteger(0, m_fibo_name, OBJPROP_LEVELSTYLE, i, STYLE_SOLID);
      ObjectSetInteger(0, m_fibo_name, OBJPROP_LEVELWIDTH, i, 1);
   }
   
   ChartRedraw();
   return true;
}

//+------------------------------------------------------------------+
//| Remove Fibonacci from chart                                    |
//+------------------------------------------------------------------+
void CFibonacciEngine::RemoveFibonacci(void)
{
   if(ObjectFind(0, m_fibo_name) >= 0)
   {
      ObjectDelete(0, m_fibo_name);
      ChartRedraw();
   }
   m_fibo_exists = false;
}

//+------------------------------------------------------------------+
//| Check if price is at specific level                            |
//+------------------------------------------------------------------+
bool CFibonacciEngine::IsPriceAtLevel(double price, double fib_level, double &level_price)
{
   level_price = CalculateLevelPrice(fib_level);
   double offset = PipsToPrice(m_level_offset_pips);

   bool result = (price >= level_price - offset && price <= level_price + offset);

   // Debug output for detailed analysis
   static int debug_counter = 0;
   debug_counter++;
   if(debug_counter % 100 == 0) // Every 100th call
   {
      double distance = MathAbs(price - level_price);
      double distance_pips = distance / SymbolInfoDouble(m_symbol, SYMBOL_POINT);
      if(Digits() == 3 || Digits() == 5) distance_pips /= 10;

      Print("IsPriceAtLevel Debug: Level=", DoubleToString(fib_level, 3),
            " LevelPrice=", DoubleToString(level_price, Digits()),
            " CurrentPrice=", DoubleToString(price, Digits()),
            " Offset=", DoubleToString(offset, Digits()),
            " Distance=", DoubleToString(distance_pips, 1), " pips",
            " Match=", result);
   }

   return result;
}

//+------------------------------------------------------------------+
//| Check if price is at entry level                               |
//+------------------------------------------------------------------+
bool CFibonacciEngine::IsPriceAtEntryLevel(double price, double &level_price, double &fib_level)
{
   static datetime last_debug_time = 0;
   bool debug_output = (TimeCurrent() != last_debug_time);

   if(debug_output)
   {
      Print("=== IsPriceAtEntryLevel Debug ===");
      Print("Current price: ", DoubleToString(price, Digits()));
      Print("Entry levels array size: ", ArraySize(m_entry_levels));
      Print("Level offset pips: ", m_level_offset_pips);
   }

   for(int i = 0; i < ArraySize(m_entry_levels); i++)
   {
      double test_level_price;
      bool at_level = IsPriceAtLevel(price, m_entry_levels[i], test_level_price);

      if(debug_output)
      {
         double offset = PipsToPrice(m_level_offset_pips);
         double distance = MathAbs(price - test_level_price);
         double distance_pips = distance / SymbolInfoDouble(m_symbol, SYMBOL_POINT);
         if(Digits() == 3 || Digits() == 5) distance_pips /= 10;

         Print("Entry Level ", DoubleToString(m_entry_levels[i], 3), ": Price=", DoubleToString(test_level_price, Digits()),
               " | Distance=", DoubleToString(distance_pips, 1), " pips | Offset=", DoubleToString(offset * 10000, 1), " pips | Match=", at_level);
      }

      if(at_level)
      {
         fib_level = m_entry_levels[i];
         level_price = test_level_price;
         Print("*** ENTRY LEVEL MATCH FOUND ***");
         Print("Fibonacci level: ", DoubleToString(fib_level, 3));
         Print("Level price: ", DoubleToString(level_price, Digits()));
         Print("Current price: ", DoubleToString(price, Digits()));
         return true;
      }
   }

   if(debug_output)
   {
      Print("No entry level matches found");
      Print("================================");
      last_debug_time = TimeCurrent();
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check if price is at exit level                                |
//+------------------------------------------------------------------+
bool CFibonacciEngine::IsPriceAtExitLevel(double price, double &level_price, double &fib_level)
{
   for(int i = 0; i < ArraySize(m_exit_levels); i++)
   {
      if(IsPriceAtLevel(price, m_exit_levels[i], level_price))
      {
         fib_level = m_exit_levels[i];
         return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| Check if price is at stop level                                |
//+------------------------------------------------------------------+
bool CFibonacciEngine::IsPriceAtStopLevel(double price, double &level_price)
{
   return IsPriceAtLevel(price, m_stop_level, level_price);
}

//+------------------------------------------------------------------+
//| Get price for specific Fibonacci level                         |
//+------------------------------------------------------------------+
double CFibonacciEngine::GetLevelPrice(double fib_level)
{
   return CalculateLevelPrice(fib_level);
}

//+------------------------------------------------------------------+
//| Get level by index                                             |
//+------------------------------------------------------------------+
FibLevel CFibonacciEngine::GetLevel(int index)
{
   FibLevel empty_level = {0, 0, "", false, false, false};
   
   if(index >= 0 && index < m_level_count)
      return m_levels[index];
      
   return empty_level;
}

//+------------------------------------------------------------------+
//| Calculate all Fibonacci levels                                 |
//+------------------------------------------------------------------+
void CFibonacciEngine::CalculateLevels(void)
{
   m_level_count = 0;
   
   // Standard Fibonacci levels
   double standard_levels[] = {0.0, 0.236, 0.382, 0.5, 0.618, 0.764, 1.0};
   
   for(int i = 0; i < ArraySize(standard_levels); i++)
   {
      bool is_entry = false;
      bool is_exit = false;
      bool is_stop = false;
      
      // Check if this is an entry level
      for(int j = 0; j < ArraySize(m_entry_levels); j++)
      {
         if(MathAbs(standard_levels[i] - m_entry_levels[j]) < 0.001)
         {
            is_entry = true;
            break;
         }
      }
      
      // Check if this is an exit level
      for(int j = 0; j < ArraySize(m_exit_levels); j++)
      {
         if(MathAbs(standard_levels[i] - m_exit_levels[j]) < 0.001)
         {
            is_exit = true;
            break;
         }
      }
      
      // Check if this is stop level
      if(MathAbs(standard_levels[i] - m_stop_level) < 0.001)
         is_stop = true;
      
      string name = DoubleToString(standard_levels[i] * 100, 1) + "%";
      CreateFibLevel(standard_levels[i], name, is_entry, is_exit, is_stop);
   }
   
   // Add extension levels
   double extension_levels[] = {1.272, 1.414, 1.618, 2.0};
   for(int i = 0; i < ArraySize(extension_levels); i++)
   {
      bool is_exit = false;
      
      // Check if this is an exit level
      for(int j = 0; j < ArraySize(m_exit_levels); j++)
      {
         if(MathAbs(extension_levels[i] - m_exit_levels[j]) < 0.001)
         {
            is_exit = true;
            break;
         }
      }
      
      string name = DoubleToString(extension_levels[i] * 100, 1) + "%";
      CreateFibLevel(extension_levels[i], name, false, is_exit, false);
   }
}

//+------------------------------------------------------------------+
//| Calculate price for Fibonacci level                            |
//+------------------------------------------------------------------+
double CFibonacciEngine::CalculateLevelPrice(double fib_level)
{
   // Fibonacci retracements: 0% = End (latest swing), 100% = Start (previous swing)
   // Level 0.382 means 38.2% retracement from End towards Start

   double range = m_swing_start.price - m_swing_end.price;
   double level_price = m_swing_end.price + (range * fib_level);

   // Debug output for first few calculations
   static int debug_count = 0;
   debug_count++;
   if(debug_count <= 10)
   {
      Print("CalculateLevelPrice Debug #", debug_count, ":");
      Print("  Swing Start: ", DoubleToString(m_swing_start.price, Digits()));
      Print("  Swing End: ", DoubleToString(m_swing_end.price, Digits()));
      Print("  Range: ", DoubleToString(range, Digits()));
      Print("  Fib Level: ", DoubleToString(fib_level, 3));
      Print("  Calculated Price: ", DoubleToString(level_price, Digits()));
      Print("  Trend: ", m_is_uptrend ? "Uptrend" : "Downtrend");
   }

   return level_price;
}

//+------------------------------------------------------------------+
//| Create Fibonacci level                                         |
//+------------------------------------------------------------------+
void CFibonacciEngine::CreateFibLevel(double level, string name, bool is_entry, bool is_exit, bool is_stop)
{
   if(m_level_count >= ArraySize(m_levels))
      return;
      
   m_levels[m_level_count].level = level;
   m_levels[m_level_count].price = CalculateLevelPrice(level);
   m_levels[m_level_count].name = name;
   m_levels[m_level_count].is_entry = is_entry;
   m_levels[m_level_count].is_exit = is_exit;
   m_levels[m_level_count].is_stop = is_stop;
   
   m_level_count++;
}

//+------------------------------------------------------------------+
//| Convert pips to price                                          |
//+------------------------------------------------------------------+
double CFibonacciEngine::PipsToPrice(int pips)
{
   double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS);

   double result;
   // For 5-digit brokers (0.00001) and 3-digit brokers (0.001), 1 pip = 10 points
   if(digits == 3 || digits == 5)
      result = pips * point * 10;
   else
      result = pips * point;

   // Debug output
   static bool first_call = true;
   if(first_call)
   {
      Print("PipsToPrice Debug: Pips=", pips, " Point=", DoubleToString(point, digits+1),
            " Digits=", digits, " Result=", DoubleToString(result, digits));
      first_call = false;
   }

   return result;
}

//+------------------------------------------------------------------+
//| Print Fibonacci levels for debugging                           |
//+------------------------------------------------------------------+
void CFibonacciEngine::PrintFibonacciLevels(void)
{
   Print("=== Fibonacci Levels ===");
   Print("Trend: ", m_is_uptrend ? "Uptrend" : "Downtrend");
   Print("Start (100%): ", DoubleToString(m_swing_start.price, Digits()), " | Time: ", TimeToString(m_swing_start.time));
   Print("End (0%): ", DoubleToString(m_swing_end.price, Digits()), " | Time: ", TimeToString(m_swing_end.time));
   Print("Range: ", DoubleToString(m_swing_start.price - m_swing_end.price, Digits()));

   // Verify key levels manually
   Print("--- Manual Verification ---");
   Print("0% (End): ", DoubleToString(m_swing_end.price, Digits()));
   Print("100% (Start): ", DoubleToString(m_swing_start.price, Digits()));

   double range = m_swing_start.price - m_swing_end.price;
   Print("38.2% Retracement: ", DoubleToString(m_swing_end.price + (range * 0.382), Digits()));
   Print("50.0% Retracement: ", DoubleToString(m_swing_end.price + (range * 0.5), Digits()));
   Print("61.8% Retracement: ", DoubleToString(m_swing_end.price + (range * 0.618), Digits()));
   Print("---------------------------");

   for(int i = 0; i < m_level_count; i++)
   {
      string flags = "";
      if(m_levels[i].is_entry) flags += "ENTRY ";
      if(m_levels[i].is_exit) flags += "EXIT ";
      if(m_levels[i].is_stop) flags += "STOP ";
      if(flags == "") flags = "STANDARD";

      Print("Level ", DoubleToString(m_levels[i].level, 3), " (", m_levels[i].name, "): ",
            DoubleToString(m_levels[i].price, Digits()), " [", flags, "]");
   }
   Print("========================");
}

//+------------------------------------------------------------------+
//| Draw swing points on chart for debugging                       |
//+------------------------------------------------------------------+
void CFibonacciEngine::DrawSwingPoints(void)
{
   string start_name = "SwingStart_" + IntegerToString(GetTickCount());
   string end_name = "SwingEnd_" + IntegerToString(GetTickCount());

   // Remove old swing markers
   ObjectDelete(0, "SwingStart");
   ObjectDelete(0, "SwingEnd");

   // Draw start point
   if(ObjectCreate(0, "SwingStart", OBJ_ARROW, 0, m_swing_start.time, m_swing_start.price))
   {
      ObjectSetInteger(0, "SwingStart", OBJPROP_ARROWCODE, 233);
      ObjectSetInteger(0, "SwingStart", OBJPROP_COLOR, clrBlue);
      ObjectSetInteger(0, "SwingStart", OBJPROP_WIDTH, 3);
   }

   // Draw end point
   if(ObjectCreate(0, "SwingEnd", OBJ_ARROW, 0, m_swing_end.time, m_swing_end.price))
   {
      ObjectSetInteger(0, "SwingEnd", OBJPROP_ARROWCODE, 234);
      ObjectSetInteger(0, "SwingEnd", OBJPROP_COLOR, clrRed);
      ObjectSetInteger(0, "SwingEnd", OBJPROP_WIDTH, 3);
   }

   // Add text labels
   string start_label = "StartLabel";
   string end_label = "EndLabel";

   ObjectDelete(0, start_label);
   ObjectDelete(0, end_label);

   if(ObjectCreate(0, start_label, OBJ_TEXT, 0, m_swing_start.time, m_swing_start.price))
   {
      ObjectSetString(0, start_label, OBJPROP_TEXT, "START");
      ObjectSetInteger(0, start_label, OBJPROP_COLOR, clrBlue);
      ObjectSetInteger(0, start_label, OBJPROP_FONTSIZE, 8);
   }

   if(ObjectCreate(0, end_label, OBJ_TEXT, 0, m_swing_end.time, m_swing_end.price))
   {
      ObjectSetString(0, end_label, OBJPROP_TEXT, "END");
      ObjectSetInteger(0, end_label, OBJPROP_COLOR, clrRed);
      ObjectSetInteger(0, end_label, OBJPROP_FONTSIZE, 8);
   }

   ChartRedraw();
}
