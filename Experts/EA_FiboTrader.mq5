//+------------------------------------------------------------------+
//|                                                EA_FiboTrader.mq5 |
//|                                  Copyright 2024, Fibonacci EA    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Fibonacci EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Fibonacci Retracement Trading Expert Advisor"

#include <Include/ZigZagDetector.mqh>
#include <Include/FibonacciEngine.mqh>
#include <Include/TradeManager.mqh>
#include <Include/RiskControl.mqh>
#include <Include/Dashboard.mqh>

//+------------------------------------------------------------------+
//| Input Parameters                                                |
//+------------------------------------------------------------------+
// ZigZag Parameters
input group "=== ZigZag Settings ==="
input int                InpZigZagDepth        = 12;           // ZigZag Depth
input int                InpZigZagDeviation    = 5;            // ZigZag Deviation
input int                InpZigZagBackstep     = 3;            // ZigZag Backstep

// Fibonacci Parameters
input group "=== Fibonacci Settings ==="
input string             InpEntryLevels        = "0.382,0.5,0.618";  // Entry Levels (comma separated)
input string             InpExitLevels         = "0.0,-0.236";       // Exit Levels (comma separated)
input double             InpStopLevel          = 0.764;              // Stop Loss Level
input int                InpLevelOffsetPips    = 2;                 // Level Offset (pips)

// Risk Management
input group "=== Risk Management ==="
input double             InpRiskPerTradePct    = 1.0;               // Risk Per Trade (%)
input int                InpMaxTrades          = 3;                 // Max Simultaneous Trades
input double             InpDailyLossLimitPct  = 5.0;               // Daily Loss Limit (%)
input double             InpMaxDrawdownPct     = 15.0;              // Max Drawdown (%)

// Filters
input group "=== Filters ==="
input bool               InpUseMAFilter        = true;              // Use MA Trend Filter
input ENUM_TIMEFRAMES    InpMATimeframe        = PERIOD_M5;         // MA Timeframe
input int                InpMAPeriod           = 200;               // MA Period
input string             InpAllowedHours       = "07:00-21:00";     // Trading Hours (local time)
input bool               InpUseNewsFilter      = false;             // Use News Filter
input int                InpMinATRPips         = 10;               // Minimum ATR (pips)

// Trade Management
input group "=== Trade Management ==="
input bool               InpUseTrailingStop    = false;             // Use Trailing Stop
input int                InpTrailingDistance   = 20;               // Trailing Distance (pips)
input bool               InpUseBreakeven       = true;              // Use Breakeven
input int                InpBreakevenDistance  = 10;               // Breakeven Distance (pips)
input bool               InpUsePartialClose    = false;             // Use Partial Close
input double             InpPartialClosePct    = 50.0;              // Partial Close (%)

// Display
input group "=== Display ==="
input bool               InpShowDashboard      = true;              // Show Dashboard
input int                InpDashboardX         = 20;               // Dashboard X Position
input int                InpDashboardY         = 20;               // Dashboard Y Position

// System
input group "=== System ==="
input int                InpMagicNumber        = 20241225;          // Magic Number
input string             InpTradeComment       = "FiboEA";          // Trade Comment
input bool               InpEnableLogging      = true;              // Enable Detailed Logging

//+------------------------------------------------------------------+
//| Global Variables                                                |
//+------------------------------------------------------------------+
CZigZagDetector*         g_zigzag;
CFibonacciEngine*        g_fibonacci;
CTradeManager*           g_trade_manager;
CRiskControl*            g_risk_control;
CDashboard*              g_dashboard;

// MA filter handle
int                      g_ma_handle;

// Timing
datetime                 g_last_bar_time;
datetime                 g_last_update_time;

// State
bool                     g_initialized = false;
string                   g_symbol;
ENUM_TIMEFRAMES          g_timeframe;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== Fibonacci EA Initialization Started ===");

   g_symbol = Symbol();
   g_timeframe = Period();
   g_last_bar_time = 0;
   g_last_update_time = 0;

   // Validate settings
   if(!ValidateSettings())
   {
      Print("Invalid EA settings detected");
      return INIT_PARAMETERS_INCORRECT;
   }

   // Print symbol information
   PrintSymbolInfo();

   // Initialize modules
   if(!InitializeModules())
   {
      Print("Failed to initialize modules");
      return INIT_FAILED;
   }

   // Setup MA filter
   if(InpUseMAFilter)
   {
      g_ma_handle = iMA(g_symbol, InpMATimeframe, InpMAPeriod, 0, MODE_SMA, PRICE_CLOSE);
      if(g_ma_handle == INVALID_HANDLE)
      {
         Print("Failed to create MA indicator");
         return INIT_FAILED;
      }
   }

   // Configure modules
   if(!ConfigureModules())
   {
      Print("Failed to configure modules");
      return INIT_FAILED;
   }

   // Create dashboard
   if(InpShowDashboard)
   {
      if(!g_dashboard.Init(g_symbol, InpDashboardX, InpDashboardY))
      {
         Print("Failed to initialize dashboard");
         return INIT_FAILED;
      }
      g_dashboard.Show();
   }

   g_initialized = true;
   Print("=== Fibonacci EA Initialized Successfully ===");
   Print("Symbol: ", g_symbol, " | Timeframe: ", EnumToString(g_timeframe));
   Print("Magic Number: ", InpMagicNumber, " | Comment: ", InpTradeComment);
   Print("Entry Levels: ", InpEntryLevels);
   Print("Exit Levels: ", InpExitLevels);
   Print("Stop Level: ", DoubleToString(InpStopLevel, 3));
   Print("Risk Per Trade: ", DoubleToString(InpRiskPerTradePct, 1), "%");
   Print("Max Trades: ", InpMaxTrades);
   Print("Trading Hours: ", InpAllowedHours);

   // Print current status
   Print(GetEAStatus());

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("=== Fibonacci EA Deinitialization ===");
   Print("Reason: ", reason);
   
   // Cleanup modules
   if(g_dashboard != NULL)
   {
      g_dashboard.Deinit();
      delete g_dashboard;
      g_dashboard = NULL;
   }
   
   if(g_trade_manager != NULL)
   {
      g_trade_manager.Deinit();
      delete g_trade_manager;
      g_trade_manager = NULL;
   }
   
   if(g_fibonacci != NULL)
   {
      g_fibonacci.Deinit();
      delete g_fibonacci;
      g_fibonacci = NULL;
   }
   
   if(g_risk_control != NULL)
   {
      g_risk_control.Deinit();
      delete g_risk_control;
      g_risk_control = NULL;
   }
   
   if(g_zigzag != NULL)
   {
      g_zigzag.Deinit();
      delete g_zigzag;
      g_zigzag = NULL;
   }
   
   // Release MA handle
   if(g_ma_handle != INVALID_HANDLE)
   {
      IndicatorRelease(g_ma_handle);
      g_ma_handle = INVALID_HANDLE;
   }
   
   g_initialized = false;
   Print("=== Fibonacci EA Deinitialized ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                            |
//+------------------------------------------------------------------+
void OnTick()
{
   if(!g_initialized)
      return;
      
   // Check for new bar
   datetime current_bar_time = iTime(g_symbol, g_timeframe, 0);
   bool new_bar = (current_bar_time != g_last_bar_time);
   
   if(new_bar)
   {
      g_last_bar_time = current_bar_time;
      OnNewBar();
   }
   
   // Update on every tick for trade management
   UpdateTradeManagement();
   
   // Update dashboard periodically
   datetime current_time = TimeCurrent();
   if(current_time != g_last_update_time)
   {
      g_last_update_time = current_time;
      if(InpShowDashboard && g_dashboard != NULL)
         g_dashboard.Update();
   }
}

//+------------------------------------------------------------------+
//| Timer function                                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
   if(!g_initialized)
      return;
      
   // Update risk control
   if(g_risk_control != NULL)
      g_risk_control.Update();
      
   // Update dashboard
   if(InpShowDashboard && g_dashboard != NULL)
      g_dashboard.Update();
}

//+------------------------------------------------------------------+
//| Chart event function                                           |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   if(!g_initialized)
      return;
      
   // Handle dashboard events
   if(InpShowDashboard && g_dashboard != NULL)
   {
      g_dashboard.OnChartEvent(id, lparam, dparam, sparam);
   }
}

//+------------------------------------------------------------------+
//| New bar handler                                                |
//+------------------------------------------------------------------+
void OnNewBar()
{
   if(InpEnableLogging)
      Print("New bar: ", TimeToString(g_last_bar_time));
   
   // Pre-checks and filters
   if(!PassPreChecks())
   {
      if(InpEnableLogging)
         Print("Pre-checks failed, skipping bar");
      return;
   }
   
   // Update ZigZag
   if(g_zigzag != NULL)
   {
      g_zigzag.Update();
      
      // Check for new swing
      if(g_zigzag.HasNewSwing())
      {
         if(InpEnableLogging)
            Print("New ZigZag swing detected");
            
         ProcessNewSwing();
      }
   }
   
   // Check for trade entries
   CheckTradeEntries();
}

//+------------------------------------------------------------------+
//| Process new ZigZag swing                                       |
//+------------------------------------------------------------------+
void ProcessNewSwing()
{
   if(g_zigzag == NULL || g_fibonacci == NULL)
      return;
      
   SwingPoint swing1, swing2;
   if(g_zigzag.GetLastTwoSwings(swing1, swing2))
   {
      if(g_zigzag.IsValidSwingPattern())
      {
         // Update Fibonacci
         if(g_fibonacci.UpdateFibonacci(swing1, swing2))
         {
            if(InpEnableLogging)
            {
               Print("Fibonacci updated: ", 
                     g_fibonacci.IsUptrend() ? "Uptrend" : "Downtrend",
                     " from ", DoubleToString(swing1.price, Digits()),
                     " to ", DoubleToString(swing2.price, Digits()));
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check for trade entry opportunities                            |
//+------------------------------------------------------------------+
void CheckTradeEntries()
{
   if(g_fibonacci == NULL || g_trade_manager == NULL || !g_fibonacci.HasValidFibonacci())
      return;
      
   double current_price = SymbolInfoDouble(g_symbol, SYMBOL_BID);
   double level_price, fib_level;
   
   // Check if price is at entry level
   if(g_fibonacci.IsPriceAtEntryLevel(current_price, level_price, fib_level))
   {
      if(InpEnableLogging)
         Print("Price at Fibonacci entry level ", fib_level, " (", DoubleToString(level_price, Digits()), ")");
         
      // Determine trade direction based on trend and retracement
      ENUM_ORDER_TYPE order_type;
      double stop_price;
      
      if(g_fibonacci.IsUptrend())
      {
         // In uptrend, buy on retracement
         order_type = ORDER_TYPE_BUY;
         stop_price = g_fibonacci.GetLevelPrice(InpStopLevel);
      }
      else
      {
         // In downtrend, sell on retracement
         order_type = ORDER_TYPE_SELL;
         stop_price = g_fibonacci.GetLevelPrice(InpStopLevel);
      }
      
      // Open trade
      if(g_trade_manager.OpenTrade(order_type, current_price, stop_price, fib_level))
      {
         Print("Trade opened at Fibonacci level ", fib_level);
      }
   }
}

//+------------------------------------------------------------------+
//| Update trade management                                         |
//+------------------------------------------------------------------+
void UpdateTradeManagement()
{
   if(g_trade_manager != NULL)
      g_trade_manager.Update();
}

//+------------------------------------------------------------------+
//| Pre-checks and filters                                         |
//+------------------------------------------------------------------+
bool PassPreChecks()
{
   // Time filter
   if(!IsWithinTradingHours())
      return false;
      
   // MA trend filter
   if(InpUseMAFilter && !PassMAFilter())
      return false;
      
   // ATR filter
   if(!PassATRFilter())
      return false;
      
   // Risk control
   if(g_risk_control != NULL && !g_risk_control.IsTradingAllowed())
      return false;
      
   return true;
}

//+------------------------------------------------------------------+
//| Check if within trading hours                                  |
//+------------------------------------------------------------------+
bool IsWithinTradingHours()
{
   if(InpAllowedHours == "")
      return true;
      
   // Parse trading hours (format: "HH:MM-HH:MM")
   string parts[];
   if(StringSplit(InpAllowedHours, '-', parts) != 2)
      return true; // Invalid format, allow trading
      
   string start_parts[], end_parts[];
   if(StringSplit(parts[0], ':', start_parts) != 2 || StringSplit(parts[1], ':', end_parts) != 2)
      return true; // Invalid format, allow trading
      
   int start_hour = (int)StringToInteger(start_parts[0]);
   int start_min = (int)StringToInteger(start_parts[1]);
   int end_hour = (int)StringToInteger(end_parts[0]);
   int end_min = (int)StringToInteger(end_parts[1]);
   
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   
   int current_minutes = dt.hour * 60 + dt.min;
   int start_minutes = start_hour * 60 + start_min;
   int end_minutes = end_hour * 60 + end_min;
   
   return (current_minutes >= start_minutes && current_minutes <= end_minutes);
}

//+------------------------------------------------------------------+
//| MA trend filter                                                |
//+------------------------------------------------------------------+
bool PassMAFilter()
{
   if(g_ma_handle == INVALID_HANDLE)
      return true;
      
   double ma_values[2];
   if(CopyBuffer(g_ma_handle, 0, 0, 2, ma_values) != 2)
      return true; // Cannot get MA values, allow trading
      
   double current_price = SymbolInfoDouble(g_symbol, SYMBOL_BID);
   
   // Simple trend filter: price should be above MA for uptrend, below for downtrend
   return true; // Simplified for now
}

//+------------------------------------------------------------------+
//| ATR volatility filter                                          |
//+------------------------------------------------------------------+
bool PassATRFilter()
{
   int atr_handle = iATR(g_symbol, g_timeframe, 14);
   if(atr_handle == INVALID_HANDLE)
      return true;
      
   double atr_values[1];
   if(CopyBuffer(atr_handle, 0, 0, 1, atr_values) != 1)
   {
      IndicatorRelease(atr_handle);
      return true;
   }
   
   double atr_pips = atr_values[0] / SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   if(Digits() == 3 || Digits() == 5)
      atr_pips /= 10;
      
   IndicatorRelease(atr_handle);
   
   return (atr_pips >= InpMinATRPips);
}

//+------------------------------------------------------------------+
//| Initialize all modules                                          |
//+------------------------------------------------------------------+
bool InitializeModules()
{
   // Initialize ZigZag detector
   g_zigzag = new CZigZagDetector();
   if(!g_zigzag.Init(g_symbol, g_timeframe, InpZigZagDepth, InpZigZagDeviation, InpZigZagBackstep))
   {
      Print("Failed to initialize ZigZag detector");
      return false;
   }
   
   // Initialize Fibonacci engine
   g_fibonacci = new CFibonacciEngine();
   if(!g_fibonacci.Init(g_symbol, g_timeframe))
   {
      Print("Failed to initialize Fibonacci engine");
      return false;
   }
   
   // Initialize risk control
   g_risk_control = new CRiskControl();
   if(!g_risk_control.Init(g_symbol))
   {
      Print("Failed to initialize risk control");
      return false;
   }
   
   // Initialize trade manager
   g_trade_manager = new CTradeManager();
   if(!g_trade_manager.Init(g_symbol, g_timeframe, InpMagicNumber))
   {
      Print("Failed to initialize trade manager");
      return false;
   }
   
   // Initialize dashboard
   g_dashboard = new CDashboard();
   
   return true;
}

//+------------------------------------------------------------------+
//| Configure all modules                                          |
//+------------------------------------------------------------------+
bool ConfigureModules()
{
   // Configure Fibonacci levels
   if(g_fibonacci != NULL)
   {
      double entry_levels[];
      double exit_levels[];
      
      ParseLevels(InpEntryLevels, entry_levels);
      ParseLevels(InpExitLevels, exit_levels);
      
      g_fibonacci.SetEntryLevels(entry_levels);
      g_fibonacci.SetExitLevels(exit_levels);
      g_fibonacci.SetStopLevel(InpStopLevel);
      g_fibonacci.SetLevelOffset(InpLevelOffsetPips);
   }
   
   // Configure risk control
   if(g_risk_control != NULL)
   {
      g_risk_control.SetRiskPerTrade(InpRiskPerTradePct);
      g_risk_control.SetMaxTrades(InpMaxTrades);
      g_risk_control.SetDailyLossLimit(InpDailyLossLimitPct);
      g_risk_control.SetMaxDrawdown(InpMaxDrawdownPct);
   }
   
   // Configure trade manager
   if(g_trade_manager != NULL)
   {
      g_trade_manager.SetRiskControl(g_risk_control);
      g_trade_manager.SetFibonacci(g_fibonacci);
      g_trade_manager.SetTrailingStop(InpUseTrailingStop, InpTrailingDistance);
      g_trade_manager.SetBreakeven(InpUseBreakeven, InpBreakevenDistance);
      g_trade_manager.SetPartialClose(InpUsePartialClose, InpPartialClosePct);
      g_trade_manager.SetComment(InpTradeComment);
   }
   
   // Configure dashboard
   if(g_dashboard != NULL)
   {
      g_dashboard.SetRiskControl(g_risk_control);
      g_dashboard.SetTradeManager(g_trade_manager);
      g_dashboard.SetFibonacci(g_fibonacci);
   }
   
   // Set timer for periodic updates
   EventSetTimer(1); // Update every second
   
   return true;
}

//+------------------------------------------------------------------+
//| Parse comma-separated levels string                            |
//+------------------------------------------------------------------+
void ParseLevels(string levels_str, double &levels[])
{
   string parts[];
   int count = StringSplit(levels_str, ',', parts);

   ArrayResize(levels, count);

   for(int i = 0; i < count; i++)
   {
      StringTrimLeft(parts[i]);
      StringTrimRight(parts[i]);
      levels[i] = StringToDouble(parts[i]);
   }
}

//+------------------------------------------------------------------+
//| Trade event handler                                            |
//+------------------------------------------------------------------+
void OnTrade()
{
   if(!g_initialized)
      return;

   // Log trade events
   if(InpEnableLogging)
   {
      Print("Trade event detected");

      // Check for closed positions
      HistorySelect(TimeCurrent() - 60, TimeCurrent()); // Last minute
      int total_deals = HistoryDealsTotal();

      for(int i = total_deals - 1; i >= 0; i--)
      {
         ulong deal_ticket = HistoryDealGetTicket(i);
         if(deal_ticket > 0)
         {
            long deal_magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);
            if(deal_magic == InpMagicNumber)
            {
               ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
               double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
               string deal_symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);

               if(deal_symbol == g_symbol && (deal_type == DEAL_TYPE_SELL || deal_type == DEAL_TYPE_BUY))
               {
                  Print("Trade closed: Ticket=", deal_ticket,
                        " Type=", EnumToString(deal_type),
                        " Profit=", DoubleToString(deal_profit, 2));
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Get EA status information                                       |
//+------------------------------------------------------------------+
string GetEAStatus()
{
   string status = "Fibonacci EA Status:\n";
   status += "Symbol: " + g_symbol + "\n";
   status += "Timeframe: " + EnumToString(g_timeframe) + "\n";
   status += "Initialized: " + (g_initialized ? "Yes" : "No") + "\n";

   if(g_risk_control != NULL)
   {
      status += "Trading Allowed: " + (g_risk_control.IsTradingAllowed() ? "Yes" : "No") + "\n";
      RiskStats stats = g_risk_control.GetStats();
      status += "Open Trades: " + IntegerToString(stats.open_trades) + "\n";
      status += "Daily P&L: $" + DoubleToString(stats.daily_pnl, 2) + "\n";
   }

   if(g_fibonacci != NULL)
   {
      status += "Fibonacci Active: " + (g_fibonacci.HasValidFibonacci() ? "Yes" : "No") + "\n";
      if(g_fibonacci.HasValidFibonacci())
      {
         status += "Trend: " + (g_fibonacci.IsUptrend() ? "Uptrend" : "Downtrend") + "\n";
      }
   }

   return status;
}

//+------------------------------------------------------------------+
//| Emergency stop function                                        |
//+------------------------------------------------------------------+
void EmergencyStop()
{
   Print("=== EMERGENCY STOP ACTIVATED ===");

   // Disable trading
   if(g_risk_control != NULL)
      g_risk_control.DisableTrading();

   // Close all trades
   if(g_trade_manager != NULL)
   {
      if(g_trade_manager.CloseAllTrades())
         Print("All trades closed successfully");
      else
         Print("Warning: Some trades may not have been closed");
   }

   // Remove Fibonacci drawings
   if(g_fibonacci != NULL)
      g_fibonacci.RemoveFibonacci();

   Print("=== EMERGENCY STOP COMPLETED ===");
}

//+------------------------------------------------------------------+
//| Validate EA settings                                           |
//+------------------------------------------------------------------+
bool ValidateSettings()
{
   // Validate ZigZag parameters
   if(InpZigZagDepth < 1 || InpZigZagDepth > 100)
   {
      Print("Invalid ZigZag Depth: ", InpZigZagDepth);
      return false;
   }

   if(InpZigZagDeviation < 1 || InpZigZagDeviation > 100)
   {
      Print("Invalid ZigZag Deviation: ", InpZigZagDeviation);
      return false;
   }

   // Validate risk parameters
   if(InpRiskPerTradePct <= 0 || InpRiskPerTradePct > 10)
   {
      Print("Invalid Risk Per Trade: ", InpRiskPerTradePct, "% (should be 0.1-10%)");
      return false;
   }

   if(InpMaxTrades < 1 || InpMaxTrades > 20)
   {
      Print("Invalid Max Trades: ", InpMaxTrades);
      return false;
   }

   // Validate Fibonacci levels
   if(InpStopLevel < 0 || InpStopLevel > 2.0)
   {
      Print("Invalid Stop Level: ", InpStopLevel);
      return false;
   }

   // Validate magic number
   if(InpMagicNumber <= 0)
   {
      Print("Invalid Magic Number: ", InpMagicNumber);
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Get symbol information                                          |
//+------------------------------------------------------------------+
void PrintSymbolInfo()
{
   Print("=== Symbol Information ===");
   Print("Symbol: ", g_symbol);
   Print("Digits: ", Digits());
   Print("Point: ", DoubleToString(Point(), Digits()));
   Print("Tick Size: ", DoubleToString(SymbolInfoDouble(g_symbol, SYMBOL_TRADE_TICK_SIZE), Digits()));
   Print("Tick Value: ", DoubleToString(SymbolInfoDouble(g_symbol, SYMBOL_TRADE_TICK_VALUE), 2));
   Print("Min Lot: ", DoubleToString(SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_MIN), 2));
   Print("Max Lot: ", DoubleToString(SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_MAX), 2));
   Print("Lot Step: ", DoubleToString(SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_STEP), 2));
   Print("Spread: ", IntegerToString(SymbolInfoInteger(g_symbol, SYMBOL_SPREAD)));
   Print("=========================");
}
