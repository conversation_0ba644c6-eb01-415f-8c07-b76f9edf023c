# Fibonacci Retracement EA – Konzept (MetaTrader 5)

## 1 Zielsetzung
Markante Price-<PERSON><PERSON>, automatisiert <PERSON>acci-Retracements zeichnen und an frei wählbaren Fibonacci-Levels Trades eröffnen, verwalten und schließen. Support für Forex-Majors und NASDAQ-CFD auf dem M1-Chart. Alle Kernparameter sind benutzerdefinierbar.

## 2 Architekturübersicht
```mermaid
flowchart TD
    A(Pre-Check & Filter) -->|OK| B(Swing Detector<br>ZigZag)
    B --> C(Fibonacci Engine)
    C --> D(Trade Decision Logic)
    D -->|Entry/Exit/SL/TP| E(Trade Manager)
    E --> F(MQL5 Order API)
    E --Update--> G(Chart & Dashboard GUI)
    subgraph Services
        H(Risk Control)
        I(Settings Loader)
        J(Journal & Telemetry)
    end
    I --> A
    I --> B
    I --> D
    H --> D
    H --> E
    D --> J
    E --> J
```

## 3 Modulbeschreibung
### 3.1 Settings Loader
* EA-Inputs gruppiert, Export/Import von <PERSON> (`.set`)
* Sinnvolle Defaults (siehe Abschnitt 8)

### 3.2 Swing Detector
* Standard: ZigZag – Depth = 12, Deviation = 5, Backstep = 3  
* Erweiterbar: ATR-Schwellen, manuelle Markierung

### 3.3 Fibonacci Engine
* Erstellt/aktualisiert Fibo-Objekt auf Basis der letzten abgeschlossenen Swings
* Unterstützt Extensions & Expansions

### 3.4 Trade Decision Logic
* Entry bei Berührung/Kreuzung eines wählbaren Fibo-Levels ± Offset
* Exit per Level, Zeit-Close, Trailing-Stop oder Break-Even
* Order-Typen: Market, Stop, Limit

### 3.5 Pre-Check & Filter
* Zeitfilter, Newsfilter, Trendfilter (SMA200 M5), Volatilitätsfilter (ATR-Min)

### 3.6 Trade Manager
* Verwaltung offener Orders, Teil-Schließungen
* Dynamische Lotberechnung (%-Risk)
* SL an Fibo-Level ± Offset
* GUI-Panel mit Statusanzeige & Buttons

### 3.7 Risk Control
* Max Trades/Chart, Daily-Loss-Limit, Equity/Margin-Check

### 3.8 Journal & Telemetry
* Strukturierte Logs, optional InfluxDB → Grafana

## 4 Benutzerparameter (Auszug)

| Gruppe     | Parameter           | Typ           | Default             | Beschreibung                           |
|------------|---------------------|---------------|---------------------|----------------------------------------|
| ZigZag     | Depth, Deviation, Backstep | int,int,int | 12, 5, 3           | Swing-Erkennung                        |
| Fibonacci  | EntryLevels[]       | double list   | 0.382, 0.5, 0.618   | Mehrfach-Entry möglich                 |
|            | ExitLevels[]        | double list   | 0.0, -0.236         | Standard-Ziel                          |
|            | StopLevel           | double        | 0.764               | SL-Level                               |
|            | LevelOffsetPips     | int           | 2                   | ± Pips zum Level                       |
| Risk       | RiskPerTradePct     | double        | 1.0                 | % Equity                               |
|            | MaxTrades           | int           | 3                   | Gleichzeitige Trades pro Symbol       |
| Filters    | UseMAFilter         | bool          | true                | Trendfilter mit SMA200                 |
|            | AllowedHours        | string        | 07:00-21:00         | Handelszeit (lokal)                    |
| Extras     | ShowDashboard       | bool          | true                | GUI-Panel sichtbar                     |

## 5 Prozessablauf

1. `OnTick` → Pre-Checks  
2. Neuer ZigZag-Swing bestätigt → Fibonacci zeichnen/aktualisieren  
3. Preis erreicht `EntryLevel ± Offset` → Orderplatzierung  
4. Laufende Überwachung für SL/TP, Teil-TP & Trailing  
5. `OnTimer` → Dashboard-Refresh, Log-Flush

## 6 Erweiterungen

* Multi-Timeframe-Bestätigung  
* ML-basierte Optimierung der ZigZag-Parameter  
* Genetischer Optimierer im EA  
* REST-API zur Signalweitergabe

## 7 Test- & Optimierungsplan

* Walk-Forward-Optimierung 2020-2024 (EURUSD M1)  
* Out-of-Sample 2024-2025  
* Monte-Carlo-Simulation (Spreads/Slippage)  
* Ziel-KPIs: Profit Factor > 1.6, DD < 15 %, Sharpe > 1.0

## 8 Empfohlene Default-Parameter

```
ZigZag_Depth      = 12
ZigZag_Deviation  = 5
ZigZag_Backstep   = 3
EntryLevels       = {0.382, 0.5, 0.618}
ExitLevels        = {0.0, -0.236}
StopLevel         = 0.764
LevelOffsetPips   = 2
RiskPerTradePct   = 1.0
UseMAFilter       = true
AllowedHours      = "07:00-21:00"
MaxTrades         = 3
```

## 9 Dateistruktur (Vorschlag)

* [`ZigZagDetector.mqh`](ZigZagDetector.mqh:1)  
* [`FibonacciEngine.mqh`](FibonacciEngine.mqh:1)  
* [`TradeManager.mqh`](TradeManager.mqh:1)  
* [`RiskControl.mqh`](RiskControl.mqh:1)  
* [`Dashboard.mqh`](Dashboard.mqh:1)  
* [`EA_FiboTrader.mq5`](EA_FiboTrader.mq5:1) (Haupt-EA)

---

Ende des Konzepts.