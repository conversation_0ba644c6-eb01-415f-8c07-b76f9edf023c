# Fibonacci Retracement EA für MetaTrader 5

Ein professioneller Expert Advisor, der automatisch Fibonacci-Retracements basierend auf ZigZag-Swings zeichnet und an konfigurierbaren Fibonacci-Levels handelt.

## 🎯 Hauptfunktionen

- **Automatische ZigZag-Swing-Erkennung** mit konfigurierbaren Parametern
- **Dynamische Fibonacci-Retracement-Zeichnung** auf dem Chart
- **Multi-Level-Trading** an verschiedenen Fibonacci-Levels
- **Umfassendes Risikomanagement** mit Equity-basierter Positionsgrößenberechnung
- **Interaktives Dashboard** mit Echtzeit-Statistiken und Kontrollen
- **Erweiterte Filter** (Zeit, Trend, Volatilität)
- **Professionelles Trade-Management** (Trailing Stop, Breakeven, Partial Close)

## 📁 Dateistruktur

```
fibea/
├── Experts/
│   └── EA_FiboTrader.mq5          # Haupt-EA
├── Include/
│   ├── ZigZagDetector.mqh         # ZigZag-Swing-Erkennung
│   ├── FibonacciEngine.mqh        # Fibonacci-Berechnungen und -Zeichnung
│   ├── TradeManager.mqh           # Handelsverwaltung
│   ├── RiskControl.mqh            # Risikomanagement
│   └── Dashboard.mqh              # GUI-Dashboard
└── README.md                      # Diese Datei
```

## 🚀 Installation

1. **Dateien kopieren:**
   - Kopieren Sie `EA_FiboTrader.mq5` in den Ordner `MQL5/Experts/`
   - Kopieren Sie alle `.mqh` Dateien in den Ordner `MQL5/Include/`

2. **Kompilieren:**
   - Öffnen Sie MetaEditor
   - Öffnen Sie `EA_FiboTrader.mq5`
   - Drücken Sie F7 oder klicken Sie auf "Compile"

3. **EA anhängen:**
   - Öffnen Sie MetaTrader 5
   - Ziehen Sie den EA aus dem Navigator auf einen Chart
   - Konfigurieren Sie die Parameter nach Ihren Wünschen

## ⚙️ Konfiguration

### ZigZag-Einstellungen
- **Depth (12):** Mindestanzahl von Bars für Swing-Erkennung
- **Deviation (5):** Mindestabweichung in Punkten
- **Backstep (3):** Mindestabstand zwischen Swings

### Fibonacci-Einstellungen
- **Entry Levels:** Komma-getrennte Liste (z.B. "0.382,0.5,0.618")
- **Exit Levels:** Komma-getrennte Liste (z.B. "0.0,-0.236")
- **Stop Level (0.764):** Stop-Loss-Level
- **Level Offset (2 Pips):** Toleranz um Fibonacci-Levels

### Risikomanagement
- **Risk Per Trade (1.0%):** Risiko pro Trade als % des Eigenkapitals
- **Max Trades (3):** Maximale gleichzeitige Trades
- **Daily Loss Limit (5.0%):** Tägliches Verlustlimit
- **Max Drawdown (15.0%):** Maximaler Drawdown

### Filter
- **MA Filter:** Trendfilter mit gleitendem Durchschnitt
- **Trading Hours:** Handelszeiten (Format: "07:00-21:00")
- **Min ATR:** Mindestvolatilität in Pips

### Trade-Management
- **Trailing Stop:** Dynamischer Stop-Loss
- **Breakeven:** Automatisches Verschieben auf Einstandskurs
- **Partial Close:** Teilschließung bei Gewinn

## 📊 Dashboard

Das interaktive Dashboard zeigt:
- **Trading-Status:** Aktiviert/Deaktiviert
- **Offene Trades:** Anzahl aktiver Positionen
- **Täglicher P&L:** Gewinn/Verlust des Tages
- **Drawdown:** Aktueller Drawdown
- **Fibonacci-Status:** Aktiv/Inaktiv mit Trendrichtung

### Dashboard-Kontrollen
- **Close All:** Alle Trades schließen
- **Enable/Disable:** Trading aktivieren/deaktivieren

## 🔄 Funktionsweise

1. **Swing-Erkennung:** ZigZag-Algorithmus identifiziert Preisswings
2. **Fibonacci-Zeichnung:** Bei neuen Swings wird automatisch ein Fibonacci-Retracement gezeichnet
3. **Entry-Signale:** Trades werden eröffnet, wenn der Preis Fibonacci-Entry-Levels erreicht
4. **Trade-Management:** Automatische Verwaltung mit Stop-Loss, Take-Profit und erweiterten Features
5. **Exit-Signale:** Trades werden geschlossen bei Fibonacci-Exit-Levels oder anderen Bedingungen

## 📈 Empfohlene Einstellungen

### Forex Majors (EURUSD, GBPUSD, etc.)
```
ZigZag Depth: 12
ZigZag Deviation: 5
Entry Levels: 0.382,0.5,0.618
Exit Levels: 0.0,-0.236
Risk Per Trade: 1.0%
Max Trades: 3
```

### NASDAQ CFD
```
ZigZag Depth: 15
ZigZag Deviation: 8
Entry Levels: 0.382,0.618
Exit Levels: 0.0,-0.272
Risk Per Trade: 0.5%
Max Trades: 2
```

## ⚠️ Wichtige Hinweise

### Risiken
- **Automatisierter Handel:** Überwachen Sie den EA regelmäßig
- **Marktbedingungen:** Der EA funktioniert am besten in trendigen Märkten
- **Backtesting:** Testen Sie Einstellungen ausführlich vor Live-Trading

### Beste Praktiken
- **Starten Sie mit kleinen Positionen** und niedrigem Risiko
- **Überwachen Sie die Performance** täglich
- **Passen Sie Parameter** an Marktbedingungen an
- **Verwenden Sie VPS** für kontinuierlichen Betrieb

## 🛠️ Fehlerbehebung

### Häufige Probleme

**EA startet nicht:**
- Prüfen Sie, ob AutoTrading aktiviert ist
- Überprüfen Sie die Kompilierung auf Fehler
- Stellen Sie sicher, dass alle Include-Dateien vorhanden sind

**Keine Trades:**
- Überprüfen Sie die Filter-Einstellungen
- Prüfen Sie die Handelszeiten
- Stellen Sie sicher, dass Fibonacci-Levels erreicht werden

**Dashboard nicht sichtbar:**
- Aktivieren Sie "Show Dashboard" in den Einstellungen
- Prüfen Sie die Dashboard-Position (X/Y-Koordinaten)

## 📝 Changelog

### Version 1.00
- Initiale Veröffentlichung
- Vollständige ZigZag-Integration
- Fibonacci-Engine mit Multi-Level-Support
- Umfassendes Risikomanagement
- Interaktives Dashboard
- Erweiterte Filter und Trade-Management

## 📞 Support

Bei Fragen oder Problemen:
1. Überprüfen Sie die Logs im MetaTrader Journal
2. Aktivieren Sie "Enable Detailed Logging" für mehr Informationen
3. Dokumentieren Sie Ihre Einstellungen und das Problem

## 📄 Lizenz

Copyright 2024, Fibonacci EA
Alle Rechte vorbehalten.

---

**Disclaimer:** Dieser EA ist ein Handelswerkzeug. Vergangene Performance garantiert keine zukünftigen Ergebnisse. Handeln Sie verantwortungsbewusst und nur mit Kapital, dessen Verlust Sie sich leisten können.
